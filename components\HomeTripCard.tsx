import React, { useState, useEffect, useRef } from 'react';
import { View, Text, TouchableOpacity, Image } from 'react-native';
import { router } from 'expo-router';
import Button from './Button';
import { useTripStore } from '@/app/store/tripStore';
import { realtimeNotificationHelpers } from '@/utils/realtimeNotifications';
import { useMutation, useQueryClient } from '@tanstack/react-query';
import { services } from '@/services';
import Toast from 'react-native-toast-message';
import { useNotificationStore } from '@/app/store/notificationStore';
import { Realtime } from 'ably';
// COMMENTED OUT: Imports for comprehensive end trip functionality
// Uncomment these if you want to revert back to HomeTripCard handling its own API calls
// import { driverTripEndedPublisherHelpers } from '@/utils/driverTripEndedPublisher';
// import { useRide } from '@/context/RideProvider';
interface HomeTripCardProps {
  trip: any;
  currentUser: any;
  arrivalTime?: Date;
  onEndTripPress?: () => void;
}

const formatDate = (dateString: string) => {
  const date = new Date(dateString);
  const now = new Date();
  const today = new Date(now.getFullYear(), now.getMonth(), now.getDate());
  const tripDate = new Date(date.getFullYear(), date.getMonth(), date.getDate());
  
  const isToday = tripDate.getTime() === today.getTime();
  const timeString = date.toLocaleTimeString('en-US', { 
    hour: 'numeric', 
    minute: '2-digit', 
    hour12: true 
  });
  
  if (isToday) {
    return `Today at ${timeString}`;
  } else {
    return `${date.toLocaleDateString('en-US', { 
      month: 'short', 
      day: 'numeric' 
    })} at ${timeString}`;
  }
};

const HomeTripCard: React.FC<HomeTripCardProps> = ({ trip, currentUser, arrivalTime, onEndTripPress }) => {
  const {
    tripStarted,
    tripProgress,
    setTripStarted,
    setTripProgress,
    // COMMENTED OUT: State variables for comprehensive end trip functionality
    // Uncomment these if you want to revert back to HomeTripCard handling its own modals
    // resetTripState,
    // showEndTripModal,
    // setShowEndTripModal,
    // showPassengerEndTripModal,
    // setShowPassengerEndTripModal,
    // showEndTripOptions,
    // setShowEndTripOptions,
    // selectedOption,
    // setSelectedOption,
    // reportIssue,
    // setReportIssue,
    // selectedIssue,
    // setSelectedIssue,
    // somethingElse,
    // setSomethingElse,
    // customIssueText,
    // setCustomIssueText,
    // showEndTripEarly,
    // setShowEndTripEarly,
  } = useTripStore();

  // COMMENTED OUT: useRide hook for comprehensive end trip functionality
  // Uncomment this if you want to revert back to HomeTripCard handling its own state reset
  // const { resetRideState } = useRide();
  const queryClient = useQueryClient();
  const { showModal } = useNotificationStore();
  const ablyRef = useRef<Realtime | null>(null);

  // Countdown timer state
  const [countdown, setCountdown] = useState<string>('');
  const [isTimeToStart, setIsTimeToStart] = useState<boolean>(false);

  // Determine if current user is the driver
  const isDriver = currentUser?.id === trip?.driver?.id;

  // Determine trip status from API data
  const isTripStarted = trip?.status === 'ongoing' || trip?.status === 'in_progress' || tripStarted;
  const isTripCompleted = tripProgress >= 100;

  // Countdown timer logic
  useEffect(() => {
    if (!trip?.timestamp || isTripStarted) {
      setCountdown('');
      setIsTimeToStart(false);
      return;
    }

    const updateCountdown = () => {
      const now = new Date();
      const tripTime = new Date(trip.timestamp);

      const timeDiff = tripTime.getTime() - now.getTime();

      if (timeDiff <= 0) {
        // Time to start the trip
        setCountdown('');
        setIsTimeToStart(true);

        // Show modal to prompt user to start trip (only for drivers)
        if (!isTripStarted && isDriver) {
          // showModal({
          //   type: 'confirmation',
          //   title: '🚗 Time to Start Trip!',
          //   content: 'Your scheduled trip time has arrived. Are you ready to start the trip?',
          //   targetRole: 'driver',
          //   tripId: trip.id,
          //   buttons: [
          //     {
          //       text: 'Start Trip',
          //       action: 'custom',
          //       actionData: { callback: handleStartTrip },
          //       style: 'primary',
          //     },
          //     {
          //       text: 'Not Yet',
          //       action: 'close',
          //       style: 'default',
          //     },
          //   ],
          // });
        }
      } else {
        // Calculate countdown
        const hours = Math.floor(timeDiff / (1000 * 60 * 60));
        const minutes = Math.floor((timeDiff % (1000 * 60 * 60)) / (1000 * 60));
        const seconds = Math.floor((timeDiff % (1000 * 60)) / 1000);

        let countdownText = '';
        if (hours > 0) {
          countdownText = `${hours}h ${minutes}m ${seconds}s`;
        } else if (minutes > 0) {
          countdownText = `${minutes}m ${seconds}s`;
        } else {
          countdownText = `${seconds}s`;
        }

        setCountdown(countdownText);
        setIsTimeToStart(false);
      }
    };

    // Update immediately
    updateCountdown();

    // Update every second
    const interval = setInterval(updateCountdown, 1000);

    return () => clearInterval(interval);
  }, [trip?.timestamp, isTripStarted, isDriver, showModal]);

  // Set up Ably subscription for progress updates (for passengers)
  useEffect(() => {
    if (!trip?.id || !currentUser?.id || isDriver) return;

    ablyRef.current = new Realtime({
      key: process.env.EXPO_PUBLIC_ABLY_KEY as string,
      clientId: currentUser.id,
      autoConnect: true,
    });

    const tripChannel = ablyRef.current.channels.get(`trip-${trip.id}`);

    // Subscribe to progress updates
    tripChannel.subscribe("progress-update", (message) => {
      if (message.data) {
        console.log('📱 HomeTripCard: Passenger received progress update:', message.data.progress + '%');
        setTripProgress(message.data.progress);
      }
    });

    return () => {
      tripChannel.unsubscribe();
      ablyRef.current?.close();
    };
  }, [trip?.id, currentUser?.id, isDriver, setTripProgress]);

  // COMMENTED OUT: Initialize driver trip ended publisher for drivers
  // This was part of the comprehensive end trip functionality
  // Uncomment this if you want to revert back to HomeTripCard handling its own Ably publishing
  /*
  useEffect(() => {
    if (!trip?.id || !currentUser?.id || !isDriver) return;

    if (!ablyRef.current) {
      ablyRef.current = new Realtime({
        key: process.env.EXPO_PUBLIC_ABLY_KEY as string,
        clientId: currentUser.id,
        autoConnect: true,
      });
    }

    // Initialize driver trip ended publisher for drivers
    driverTripEndedPublisherHelpers.initialize(ablyRef.current);
    console.log('🚗 HomeTripCard: Driver trip ended publisher initialized');

    return () => {
      if (ablyRef.current) {
        ablyRef.current.close();
      }
    };
  }, [trip?.id, currentUser?.id, isDriver]);
  */

  // Note: Start trip is handled locally without API call



  // Cancel ride mutation (for passengers)
  const { mutate: cancelRide, isPending: isCancellingRide } = useMutation({
    mutationFn: services.cancelRideRequest,
    onSuccess: (data) => {
      Toast.show({
        type: "success",
        text1: data?.message || "Ride cancelled successfully",
      });

      queryClient.invalidateQueries({ queryKey: ["activeTrip"] });
      queryClient.invalidateQueries({ queryKey: ["requests"] });
      queryClient.invalidateQueries({ queryKey: ["trips"] });
    },
    onError: (error: any) => {
      Toast.show({
        type: "error",
        text1: error.response?.data?.description || "Failed to cancel ride",
      });
    },
  });

  const handleStartTrip = () => {
    // Check if there are passengers before starting the trip
    if (trip?.passengers?.length > 0) {
      console.log('🚀 HomeTripCard: Starting trip:', {
        tripId: trip?.id,
        tripStatus: trip?.status,
        currentTripStarted: tripStarted,
        isTripStarted
      });
      setTripStarted(true);
      Toast.show({
        type: "success",
        text1: "Your trip has started",
      });

      // Send realtime notification to passengers
      if (trip?.id && currentUser?.firstName) {
        // Pass complete trip data for proper navigation
        const completeTrip = {
          ...trip,
          driver: trip.driver || {
            id: currentUser.id,
            firstName: currentUser.firstName,
            lastName: currentUser.lastName,
            email: currentUser.email,
            phoneNumber: currentUser.phoneNumber,
            isVerified: currentUser.isVerified,
            carDetails: currentUser.carDetails
          },
          status: 'ongoing' // Mark as ongoing since trip is started
        };

        realtimeNotificationHelpers.notifyTripStarted(
          trip.id,
          currentUser.firstName,
          completeTrip // Pass complete trip object instead of subset
        );

        // Also publish direct event to trip channel (like driver notifications)
        // Note: We need access to ablyRef here, which might need to be passed as prop
        console.log('📤 HomeTripCard: Would publish trip-started to channel:', `trip-${trip.id}`);
      }
    } else {
      Toast.show({
        type: "error",
        text1: "Cannot start trip without passengers",
      });
    }
  };

  // COMMENTED OUT: End trip mutation with comprehensive functionality
  // This was causing double API calls when used alongside TripSummary's endTrip
  // Uncomment if you want to revert back to having HomeTripCard handle its own API calls
  /*
  const { mutate: endTrip, isPending: isEndingTrip } = useMutation({
    mutationFn: services.endTrip,
    onSuccess: async (data) => {
      Toast.show({
        type: "success",
        text1: data?.message,
      });

      // Reset all local end trip modal states to prevent UI issues
      setShowEndTripModal(false);
      setShowEndTripOptions(false);
      setShowEndTripEarly(false);
      setSelectedOption(null);
      setSelectedIssue(null);
      setReportIssue(false);
      setSomethingElse(false);
      setCustomIssueText('');

      setShowPassengerEndTripModal(true);

      // Send realtime notification to passengers
      if (trip?.id && currentUser?.firstName) {
        realtimeNotificationHelpers.notifyTripEnded(trip.id, currentUser.firstName);
      }

      // Publish Ably event for passenger trip ended modal
      if (trip?.id && currentUser && isDriver) {
        try {
          const endReason = selectedOption === 'option1' ? 'early' :
                           selectedIssue ? 'issue' : 'completed';

          await driverTripEndedPublisherHelpers.publishTripEnded(trip, currentUser, endReason);
          console.log('📡 HomeTripCard: Published trip-ended event to passengers');
        } catch (error) {
          console.error('❌ HomeTripCard: Failed to publish trip-ended event:', error);
        }
      }

      // Reset ride state to clear preferences and form data
      resetRideState();

      // Invalidate queries to refresh data
      queryClient.invalidateQueries({ queryKey: ["activeTrip"] });
      queryClient.invalidateQueries({ queryKey: ["trips"] });

      router.replace("/(tabs)/Home");
    },
    onError: (error: any) => {
      Toast.show({
        type: "error",
        text1: error.response?.data?.description || error.response?.data?.message || error.message,
      });
    },
  });
  */

  const handleEndTrip = () => {
    if (onEndTripPress) {
      onEndTripPress();
    } else {
      // COMMENTED OUT: Direct API call to prevent double calls
      // Uncomment the endTrip mutation above and this line if you want to revert
      // endTrip({ dataBody: { tripId: trip?.id } });
      console.warn('HomeTripCard: No onEndTripPress handler provided and direct API call is disabled');
    }
  };



  const handleCancelRide = () => {
    // Find the current user's passenger record to get their requestId
    const currentPassenger = trip?.passengers?.find((p: any) => p.id === currentUser?.id);
    if (currentPassenger?.requestId) {
      cancelRide({ dataBody: { requestId: currentPassenger.requestId } });
    } else {
      Toast.show({
        type: "error",
        text1: "Unable to cancel ride - request not found",
      });
    }
  };

  const handleExpandRide = () => {
    router.push({
      pathname: '/(ride)/TripSummary',
      params: { trip: JSON.stringify(trip) }
    });
  };

  const renderActionButtons = () => {
    if (isDriver) {
      return (
        <View className="flex-row mt-[16px] items-center">
          {/* {!isTripStarted ? (
            <Button
              text={countdown ? `Start in ${countdown}` : isTimeToStart ? "Start Trip" : "Waiting..."}
              buttonClassName={`${isTimeToStart ? "bg-[#34A853]" : "bg-gray-400"} flex-1 mr-2`}
              textClassName="text-semibold text-[12px] text-white"
              onClick={handleStartTrip}
              buttonDisabled={!isTimeToStart}
              height="h-[30px]"
            />
          ) :  (
            <Button
              text="End Trip"
              buttonClassName="bg-[#F4F4F4] flex-1 mr-2"
              textClassName="text-semibold text-[12px]"
              onClick={handleEndTrip}
              height="h-[30px]"
            />
          )} */}

          <TouchableOpacity onPress={handleExpandRide} className='bg-[#473BF0] h-[30px] w-full justify-center items-center rounded-full flex-row'>
            <Text className='text-white text-[12px] font-semibold'>Expand</Text>
            <Image source={require("../assets/images/right_line.png")} className='w-[15px] h-[15px] ml-1' tintColor="white" />

          </TouchableOpacity>

          {/* <Button
            text="Expand"
            buttonClassName="bg-[#473BF0]"
            textClassName="text-white text-[12px] font-semibold"
            width="w-[93px]"
            onClick={handleExpandRide}
            height="h-[30px]"
          /> */}
        </View>
      );
    } else {
      // Passenger view
      return (
        <View className="flex-row items-center mt-4">
          {/* {isTripStarted && (
            <Button
              text="Cancel ride"
              buttonClassName="bg-[#F4F4F4] flex-1 mr-2"
              textClassName="text-black text-[12px]"
              onClick={handleCancelRide}
              isLoading={isCancellingRide}
              buttonDisabled={isCancellingRide}
              height="h-[30px]"
            />
          )} */}

          <TouchableOpacity onPress={handleExpandRide} className='bg-[#473BF0] h-[30px] w-full justify-center items-center rounded-full flex-row'>
            <Text className='text-white text-[12px] font-semibold'>Expand</Text>
            <Image source={require("../assets/images/right_line.png")} className='w-[15px] h-[15px] ml-1' tintColor="white" />

          </TouchableOpacity>
        </View>
      );
    }
  };

  // Don't render if no trip data or missing essential properties
  if (!trip || !trip.origin || !trip.destination) return null;

  return (
    <View className=" mt-4">
      <View className="bg-white rounded-[6px] p-4 shadow-sm border border-gray-100">
        {/* Header */}
        <View className="flex-row items-center justify-between mb-[10px]">
          <View className='flex-row items-center'>
              <Image source={require("../assets/images/carr.png")} className='w-[15px] h-[15px] mr-1' />
              <Text className="font-medium text-[13px]">
                Coride
              </Text>
          </View>
          
          <View className="">
            <Text className="text-[13px] font-semibold">
              Now
            </Text>
          </View>
        </View>

        {/* Route Information */}
        <View className="">

          <View className='flex-row items-center'>
            <View className="items-center mr-1">
              <Image source={require("../assets/images/radioNew.png")} className="h-[10px] w-[10px]" />
              <View className="bg-[#B3B3B3] h-[14px] w-[1.5px] my-[1px]" />
              <Image source={require("../assets/images/blueLocationNew.png")} className="h-[12px] w-[12px]" />
            </View>
            <View className='flex-col flex-1'>
              <View className="mb-1">
                <Text numberOfLines={1} ellipsizeMode="tail" className="text-sm">
                  <Text className='font-semibold'>
                    {trip?.origin?.name?.split(",")[0] || "Origin"}
                  </Text>
                  <Text className="font-normal text-[#787A80]">
                    {trip?.origin?.name?.split(",").slice(1).join(",") ? `, ${trip?.origin?.name?.split(",").slice(1).join(",")}` : ""}
                  </Text>
                </Text>
              </View>

              <View>
                <Text numberOfLines={1} ellipsizeMode="tail" className="text-sm">
                  <Text className='font-semibold'>
                    {trip?.destination?.name?.split(",")[0] || "Destination"}
                  </Text>
                  <Text className="font-normal text-[#787A80]">
                    {trip?.destination?.name?.split(",").slice(1).join(",") ? `, ${trip?.destination?.name?.split(",").slice(1).join(",")}` : ""}
                  </Text>
                </Text>
              </View>
            </View>
            
          </View>  
        
          
          {/* Progress Bar with Moving Car */}
          {isTripStarted && (
            <View className="mt-[16px]">
              <View className="relative">
                {/* Progress Bar Background */}
                <View className="h-[4px] bg-gray-200 rounded-full">
                  <View
                    className="h-[4px] bg-[#34A853] rounded-full"
                    style={{ width: `${tripProgress}%` }}
                  />
                </View>

                {/* Moving Car Marker */}
                <View
                  className="absolute -top-[6px] transform -translate-x-1/2"
                  style={{
                    left: `${Math.min(Math.max(tripProgress, 5), 95)}%`,
                  }}
                >
                  <Image
                    source={require("../assets/images/movingCar.png")}
                    className="w-[32px] h-[16px]"
                    resizeMode="contain"
                  />
                </View>
              </View>

              {/* <Text className="text-xs text-gray-500 mt-1 text-center">
                {tripProgress}% Complete
              </Text> */}
            </View>
          )}
        </View>

        {/* Action Buttons */}
        {renderActionButtons()}
      </View>
    </View>
  );
};

export default HomeTripCard;
