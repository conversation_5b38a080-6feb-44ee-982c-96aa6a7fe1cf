import { View, Text, ActivityIndicator, Image, TouchableOpacity } from 'react-native'
import React, { useRef, useEffect, useState } from 'react'
import useActiveTrip from '@/hooks/useActiveTrip';
import { useRide } from '@/context/RideProvider';
import { router } from 'expo-router';
import { services } from '@/services';
import { useMutation } from '@tanstack/react-query';
import Toast from 'react-native-toast-message';
import useCurrentUser from '@/hooks/useCurrentUser';

const ActiveRides: React.FC = () => {
    const { data: tripData, isLoading, refetchActive } = useActiveTrip();
    const { setIsEnabled } = useRide();
    const { data: user } = useCurrentUser();

    // Only show for drivers - check if current user is the driver of the active trip
    const isDriver = user?.id === tripData?.driver?.id;

    // Don't render anything for passengers

    // Smart polling for active trip updates
    const mountTimeRef = useRef<string>('');
    const [, forceUpdate] = useState({});
    
    useEffect(() => {
        if (!mountTimeRef.current) {
            const now = new Date();
            mountTimeRef.current = now.toLocaleTimeString([], {hour: '2-digit', minute:'2-digit', hour12: true});
            forceUpdate({});
        }
    }, []);
    
    const { mutate: handleCancelTrip, isPending: isCancelingTrip } = useMutation({
        mutationFn: services.cancelTrip,
        onSuccess: (data) => {
            Toast.show({
                type: "success",
                text1: data.message || "Trip cancelled successfully",
            });
            
            setIsEnabled(false);
            
            refetchActive();
        },
        onError: (error: any) => {
            Toast.show({
                type: "error",
                text1: error.response?.data?.description || error.response?.data?.message || error.message,
            });
        },
    });

    if (isLoading) {
        return (
            <View className="flex-1 justify-center items-center">
                <ActivityIndicator size="small" color="#473BF0" />
                <Text className="mt-2 text-gray-500">Loading active trips...</Text>
            </View>
        );
    }

    // Check if we have a valid trip with required data
    if (!tripData || !tripData.origin || !tripData.destination) {
        return (
            <View className="flex-1 bg-white rounded-md  h-[400px] items-center justify-center p-6">
                <Text className="text-lg font-medium mb-2">Plan your Coride</Text>
                <Text className="text-gray-500 mb-4">Your trip activity will appear here</Text>
                <Image
                    source={require('../assets/images/homme.png')}
                    className="w-24 h-24"
                />
            </View>
        );
    }

    const trip = tripData;
    if(isDriver) {
    return (
        <TouchableOpacity onPress={() => {
            router.push({
                pathname: "/TripSummary",
                params: {
                    trip: encodeURIComponent(JSON.stringify(trip)),
                },
            });
        }} className="flex w-full flex-col bg-[#FFF] rounded-md  p-4 mx-4 mb-4">
            <Text className='text-[#8388A2] text-xs font-semibold'>Update</Text>
            
            <View className="mb-1 flex flex-row gap-20">
                <View>
                    <Text className='text-base font-semibold'>Your trip has been posted</Text>
                </View>
                <View className='flex flex-row items-center justify-center gap-1'>
                    <Text className="text-[#787A80] text-xs font-normal">{mountTimeRef.current}</Text>
                    <Image source={require("../assets/images/goNew.png")} className='w-2 h-3' />
                </View>
            </View>

            <View className='flex flex-row mb-1 items-center justify-between'>
                <Text numberOfLines={1} ellipsizeMode='tail' className="flex-1 text-sm font-medium">
                    {trip.origin?.name || 'Unknown origin'}
                </Text>
                <Image source={require("../assets/images/fromandtoNew.png")} className="mx-2 w-3 h-3" />
                <Text numberOfLines={1} ellipsizeMode='tail' className="flex-1 text-sm text-right font-medium">
                    {trip.destination?.name || 'Unknown destination'}
                </Text>
            </View>
            
            <View className="flex-row mb-2 items-center space-x-2">
                <View className='gap-1 flex-row items-center'>
                    <Image source={require("../assets/images/corideNew.png")} className='w-4 h-4' />
                    <Text className="text-sm font-medium text-[#787A80]">Coride</Text>
                </View>
                <View className='h-1 w-1 bg-[#787A80] rounded-full' />
                <View className='flex-row items-center'>
                    <Text numberOfLines={1} ellipsizeMode='tail' className="text-sm font-medium text-[#787A80]">
                        {trip.timestamp ? new Date(trip.timestamp).toLocaleDateString([], {day: '2-digit', month: '2-digit', year: 'numeric'}) : 'N/A'} - 
                    </Text>
                    <Text numberOfLines={1} ellipsizeMode='tail' className="text-sm font-medium text-[#787A80]">
                        {trip.timestamp ? new Date(trip.timestamp).toLocaleTimeString([], {hour: '2-digit', minute:'2-digit'}) : 'N/A'}
                    </Text>
                </View>
                <View className='h-1 w-1 bg-[#787A80] rounded-full' />
                <View>
                    <Text className="text-sm font-medium text-[#787A80]">₦{trip.pricePerSeat || '0'}</Text>
                </View>
            </View>

            {/* <TouchableOpacity 
                onPress={() => trip.id && handleCancelTrip(trip.id)}
                disabled={isCancelingTrip || !trip.id}
                className="bg-red-500 py-2 px-4 rounded-md self-end"
            >
                <Text className="text-white font-medium">
                    {isCancelingTrip ? 'Canceling...' : 'Cancel Trip'}
                </Text>
            </TouchableOpacity> */}
        </TouchableOpacity>
    );
    }
};

export default ActiveRides;