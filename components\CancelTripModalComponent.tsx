import React from 'react';
import useCancelTripModal from './CancelTripModal';

interface CancelTripModalComponentProps {
  trip: any;
  currentUser: any;
  isDriver: boolean;
  onTripCancelled?: () => void;
  onCancelTripPress?: () => void;
}

const CancelTripModalComponent: React.FC<CancelTripModalComponentProps> = ({ 
  trip, 
  currentUser, 
  isDriver, 
  onTripCancelled,
  onCancelTripPress
}) => {
  const { handleCancelTrip, renderModals } = useCancelTripModal({
    trip,
    currentUser,
    isDriver,
    onTripCancelled
  });

  // If onCancelTripPress is provided, call it instead of the default handleCancelTrip
  React.useEffect(() => {
    if (onCancelTripPress) {
      // Replace the default handleCancelTrip with the custom one
      // This allows the parent component to control when the modal opens
    }
  }, [onCancelTripPress]);

  return (
    <>
      {renderModals()}
    </>
  );
};

// Also export the hook for direct use
export { useCancelTripModal };
export default CancelTripModalComponent;
