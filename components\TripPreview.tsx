import { View, Text, TouchableOpacity, Image, ScrollView } from 'react-native'
import React, { useMemo, useState } from 'react'
import { Trip } from '@/utils/types'
import GoBack from './GoBack';
import { Link, router } from 'expo-router';
import { extractTimeFromISO, makePhoneCall } from '@/utils/helpers';
import Button from './Button';
import PaymentMode from './PaymentMode';
import InteractiveTripMap from './InteractiveTripMap';
import { useUser } from '@/context/UserContext';
import useCurrentUser from '@/hooks/useCurrentUser';
import ably from '@/utils/ably';
import { useMutation } from "@tanstack/react-query";
import { services } from "@/services";
import Toast from "react-native-toast-message";

interface TripPreviewProps {
  step: number;
  setStep: (step: number) => void;
  setShowPassenger: (show: boolean) => void;
  trip: Trip | null;
  setCancelRide: (cancel: boolean) => void;
}

const calculateDistance = (lat1: number, lon1: number, lat2: number, lon2: number): number => {
  const R = 6371;
  const dLat = deg2rad(lat2 - lat1);
  const dLon = deg2rad(lon2 - lon1);
  const a = Math.sin(dLat / 2) * Math.sin(dLat / 2) +
      Math.cos(deg2rad(lat1)) * Math.cos(deg2rad(lat2)) *
      Math.sin(dLon / 2) * Math.sin(dLon / 2);
  const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1 - a));
  return R * c;
};

const deg2rad = (deg: number): number => deg * (Math.PI / 180);

const formatDate = (timestamp: string): string => {
  const date = new Date(timestamp);
  const days = ['Sunday', 'Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday', 'Saturday'];
  const months = ['January', 'February', 'March', 'April', 'May', 'June', 'July', 'August', 'September', 'October', 'November', 'December'];
  
  const dayOfWeek = days[date.getDay()];
  const day = date.getDate();
  const month = months[date.getMonth()];
  
  let hours = date.getHours();
  const minutes = date.getMinutes().toString().padStart(2, '0');
  const ampm = hours >= 12 ? 'pm' : 'am';
  hours = hours % 12;
  hours = hours ? hours : 12;
  
  return `${dayOfWeek}, ${day} ${month} at ${hours}:${minutes} ${ampm}`;
};

const capitalizeFirstLetter = (text: string): string => {
  return text.split(" ").map(word => 
      word.charAt(0).toUpperCase() + word.slice(1).toLowerCase()
  ).join(" ");
};

interface TripRouteCardProps {
  trip: Trip;
  arrivalTime: Date | null;
}

interface TripDetailsCardProps {
  trip: Trip;
}

const TripRouteCard: React.FC<TripRouteCardProps> = ({ trip, arrivalTime }) => (
  <View className="mt-2 w-full justify-center items-center">
      <View className="bg-white flex-row items-center rounded-[6px] px-2 py-4">
          <View className="flex-1 gap-y-[2px] ml-3">
              <View className="flex flex-row gap-2 items-center">
                  <View className="h-[30px] w-[2px] bg-[#473BF0]" />
                  <View className='flex-1 flex-col'>
                      <View className='flex flex-row items-center justify-between'>
                          <Text className='font-semibold text-sm'>
                              {trip?.origin?.name.split(' ')[0]}
                          </Text>
                          <Text className="font-medium text-sm">
                              {formatDate(trip.timestamp)}
                          </Text>
                      </View>
                      <Text numberOfLines={1} ellipsizeMode="tail" className="text-sm font-normal text-[#787A80]">
                          {trip?.origin?.name}
                      </Text>
                  </View>
              </View>
              <View className="flex flex-row gap-2 items-center">
                  <View className="h-[30px] w-[2px] bg-[#34A853]" />
                  <View className='flex-1 flex-col'>
                      <View className='flex flex-row items-center justify-between'>
                          <Text className='font-semibold text-sm'>
                              {trip?.destination?.name.split(' ')[0]}
                          </Text>
                          <Text className="font-medium text-sm">
                              {arrivalTime && (
                                  <Text>~{formatDate(arrivalTime.toISOString()).split('at ')[1]}</Text>
                              )}
                          </Text>
                      </View>
                      <Text numberOfLines={1} ellipsizeMode="tail" className="text-sm font-normal text-[#787A80]">
                          {trip?.destination?.name}
                      </Text>
                  </View>
              </View>
          </View>
      </View>
  </View>
);

const TripDetailsCard: React.FC<TripDetailsCardProps> = ({ trip }) => (
  <View className='flex flex-row mt-2 w-full justify-between items-center space-x-2'>
      <View className='flex-1 flex-row justify-between bg-white p-4 rounded-[6px]'>
          <Text className='text-[#787A80] font-semibold text-sm'>Seat left:</Text>
          <Text className='font-semibold text-sm'>{trip?.noOfPassengers - trip?.passengers.length} of {trip?.noOfPassengers}</Text>
      </View>
      <View className='flex-1 flex-row justify-between items-center bg-white p-4 rounded-[6px]'>
          <Text className='text-[#787A80] font-semibold text-sm'>Price per seat:</Text>
          <Text className='text-[#473BF0] font-semibold text-sm'>N{trip?.pricePerSeat}</Text>
      </View>
  </View>
);

const TripPreview: React.FC<TripPreviewProps> = ({
  step,
  setStep,
  setShowPassenger,
  trip,
  setCancelRide
}) => {

  const [showSeats, setShowSeats] = useState(false)
  const [showDriverDetails, setShowDriverDetails] = useState(false)
  const [bookingRide, setBookingRide] = useState(false)
  const [confirmBookingRide, setConfirmBookingRide] = useState(false)
  const { ride, displayCurrentAddress, initialRegion } = useUser();

  const { data: user, isLoading: userDetailsLoading } = useCurrentUser();

  const { mutate: request, isPending } = useMutation({
    mutationFn: services.requestRide,
    onSuccess: (data) => {
      Toast.show({
        type: "success",
        text1: data.message,
      });

      // Publish ride request to drivers channel for real-time notifications
      if (trip?.id && user) {
        console.log('📤 Publishing ride request with user data:', {
          firstName: user.firstName,
          lastName: user.lastName,
          tripId: trip.id
        });

        const channel = ably.channels.get("drivers");
        channel.publish("ride-request", {
          tripId: trip.id,
          requestId: data.requestId || `req_${Date.now()}`,
          firstName: user.firstName || 'Unknown',
          lastName: user.lastName || 'User',
          pickup: ride.pickup,
          dropoff: ride.dropoff,
          requestedAt: new Date().toISOString(),
          modeOfPayment: ride.modeOfPayment,
        });
      }

      setConfirmBookingRide(true);
      setShowPassenger(true);
      setTimeout(() => {
        router.replace("/(drawer)/(tabs)/Home");
      }, 2000);
    },
    onError: (error: any) => {
      Toast.show({
        type: "error",
        text1: error.response
          ? error.response.data.description || error.response.data.message
          : error.message,
      });
    },
  });

  const handleGoBack = () => {
    if (step === 1) {
      router.back();
    } else {
      setStep(step - 1);
    }
  };

  const arrivalTime = useMemo(() => {
    if (!trip?.origin || !trip?.destination) return null;
    
    const distance = calculateDistance(
        trip.origin.lat,
        trip.origin.lng,
        trip.destination.lat,
        trip.destination.lng
    );
    
    const averageSpeed = 60;
    const travelTimeHours = distance / averageSpeed;
    const departureTime = new Date(trip.timestamp);
    
    return new Date(departureTime.getTime() + (travelTimeHours * 60 * 60 * 1000));
}, [trip]);

  if (!trip) {
    return (
      <View className="flex-1 justify-center items-center bg-white">
        <Text className="text-lg font-medium">No trip details available</Text>
      </View>
    );
  }

  if (showSeats) {
    return (
      <View className='flex-1 bg-[#F4F4F4]'>
        <View className='flex-row items-center px-4 pb-2 bg-[#F4F4F4]'>
          <TouchableOpacity onPress={() => setShowSeats(false)}>
            <GoBack color='#FFFFFF' />
          </TouchableOpacity>
          <View className='flex-1 flex-row justify-center items-center'>
            <Text className='text-base font-semibold text-black'>Seats</Text>
          </View>
        </View>
        <View className='px-4 mt-4'>
          <Text className='text-2xl font-semibold'>Meet your Coriders</Text>
          <Text className='text-base font-normal'>Here is everyone that will be joining your trip</Text>
        </View>

        <View className='px-4 flex-row flex-wrap gap-y-5 gap-x-11 justify-around mt-4'>
          <View className="items-center">
            <Image
              source={require("../assets/images/ProfilePic.png")}
              className="h-[80px] w-[80px] rounded-full mb-1"
              resizeMode="cover"
            />
            <Text className="text-xs text-[#787A80] text-center">
              {`${trip.driver.firstName}`}
            </Text>
            <Text className="text-xs text-[#787A80] text-center">
              {`${trip.driver.lastName}`}
            </Text>
            <Text className="text-xs text-[#787A80] text-center">
              (Driver)
            </Text>
          </View>
          {Array.from({ length: trip.driver.carDetails.passengerCapacity }).map((_, index) => {
            const passenger = trip.passengers[index];
            return (
              <View key={index} className="items-center">
                <Image
                  source={passenger ? require("../assets/images/ProfilePic.png") : require("../assets/images/emptyNew.png")}
                  className="h-[80px] w-[80px] rounded-full mb-1"
                  resizeMode="cover"
                />
                <Text className="text-xs text-[#787A80] text-center">
                  {passenger ? `${passenger.firstName}${passenger.lastName}` : "Empty seat"}
                </Text>
              </View>
            );
          })}
        </View>
      </View>
    )
  }

  if (showDriverDetails) {
    return (
      <View className='flex-1 bg-[#F4F4F4]'>
        <View className='flex-row items-center px-4 pb-2 bg-[#F4F4F4]'>
          <TouchableOpacity onPress={() => setShowDriverDetails(false)}>
            <GoBack color='#FFFFFF' />
          </TouchableOpacity>
          <View className='flex-1 flex-row justify-center items-center'>
            <Text className='text-base font-semibold text-black'>{trip.driver.firstName}'s profile</Text>
          </View>
        </View>
        <View className='p-4 mx-4 flex mt-4 bg-white rounded-md'>
        <View className='flex flex-row items-center justify-between'>
            <View className='flex-row items-center'>
              <Image
                  source={require("../assets/images/ProfilePic.png")}
                  className="h-[56px] w-[56px] rounded-full mr-3"
                  resizeMode="cover"
                />
                <View className='flex justify-center'>
                  <Text className="text-[#151B2D] font-semibold text-[16px]">
                    {`${trip.driver.firstName}${trip.driver.lastName}`}
                  </Text>
                  <View className='flex-row items-center justify-center'>
                    <Text className="text-[#787A80] font-normal text-[14px]">
                      {`${trip.driver.carDetails.make}${trip.driver.carDetails.model}, ${trip.driver.carDetails.colour}`}
                    </Text>
                    <View>
                    <Text className='bg-[#F4F4F4] text-[11px] font-medium p-1 rounded-md ml-1'>{trip.driver.carDetails.plateNumber}</Text>
                    </View>
                    
                  </View>
                </View>
            </View>
            
          </View>
          <View className='bg-[#ECECEC] h-[1px] w-[90%] mt-2' />
          <TouchableOpacity onPress={() => console.log(user)}>
            <Text>user</Text>
          </TouchableOpacity>
        </View>
      </View>
    )
  }

  const handleBookRide = () => {
    request({
      dataBody: {
        tripId: trip.id,
        modeOfPayment: ride.modeOfPayment,
        currentLocation: {
          name: displayCurrentAddress,
          lat: initialRegion.latitude,
          lng: initialRegion.longitude,
        },
        pickup: ride.pickup,
        dropoff: ride.dropoff,
        cardId: user?.cards[0]?.id,
      },
    });
  };

  return (
    <View className="flex-1 bg-[#F4F4F4]">
      <View className='flex-row items-center px-4 pb-2 bg-[#F4F4F4]'>
        <TouchableOpacity onPress={handleGoBack}>
          <GoBack color='#FFFFFF' />
        </TouchableOpacity>
        <View className='flex-1 flex-row justify-center items-center'>
          <Text className='text-base font-semibold text-black'>Trip Preview</Text>
        </View>
      </View>

      <ScrollView className="flex-1 px-4" showsVerticalScrollIndicator={false}>
        <TripRouteCard trip={trip} arrivalTime={arrivalTime} />
        <TripDetailsCard trip={trip} />
        <TouchableOpacity className='flex-row items-center justify-between mt-2 bg-white rounded-md p-4' onPress={() => setShowSeats(true)}>
          <Text className='text-[#999999] font-medium text-sm'>Booked by:</Text>
          <View className='flex-row items-center'>
            {trip.passengers.length > 0 ? (
              <Image
                source={require("../assets/images/ProfilePic.png")}
                className="h-[28px] w-[28px] rounded-full mr-3"
                resizeMode="cover"
              />
            ) : (
              <Image
                source={require("../assets/images/emptyNew.png")}
                className="h-[28px] w-[28px] rounded-full mr-3"
                resizeMode="cover"
              />
            )}
            <Image source={require("../assets/images/goNew.png")} className="h-[20px] w-[20px]" />
          </View>
        </TouchableOpacity>
        {/* <TouchableOpacity className='bg-white rounded-lg p-4 shadow-sm' onPress={() => {
          console.log(trip);
        }}>
          <Text className='text-[#473BF0] font-semibold text-sm'>View Driver Information</Text>
        </TouchableOpacity> */}
        <TouchableOpacity className="flex-1 mt-2 bg-white rounded-md p-4" onPress={() => setShowDriverDetails(true)}>
          <View className='flex-1 flex-row items-center justify-between'>
            <View className='flex-row items-center'>
              <Image
                  source={require("../assets/images/ProfilePic.png")}
                  className="h-[40px] w-[40px] rounded-full mr-3"
                  resizeMode="cover"
                />
                <View className='flex justify-center'>
                  <Text className="text-[#151B2D] font-semibold text-[16px]">
                    {`${trip.driver.firstName}${trip.driver.lastName}`}
                  </Text>
                  <View className='flex-row items-center'>
                    <Text className="text-[#787A80] font-normal text-[14px]">
                      {`${trip.driver.carDetails.make}${trip.driver.carDetails.model}, ${trip.driver.carDetails.colour}`}
                    </Text>
                    <Text className='bg-[#F4F4F4] text-[11px] font-medium p-1 rounded-md ml-2 '>{trip.driver.carDetails.plateNumber}</Text>
                  </View>
                </View>
            </View>
            <Image source={require("../assets/images/goNew.png")} className='h-[20px] w-[20px]' />
            
          </View>
          <View className='bg-[#ECECEC] h-[1px] w-[90%] mt-2' />
          <View className="flex-col justify-center mt-3">
            <Text className="text-[#787A80] text-xs font-medium">Driver's note: </Text>
            <Text className="text-[#151B2D] text-sm font-normal flex-1">{trip.driverNote || "Please keep to time"}</Text>
          </View>
        </TouchableOpacity>

        {trip.preferences && trip.preferences.length > 0 && (
          <View className="mt-2 bg-white rounded-md p-4 shadow-sm">
            <Text className="text-[#787A80] font-medium text-sm mb-3">Trip preference</Text>
            <View className="flex-col">
              {trip.preferences.map((preference, index) => (
                <View
                  key={index}
                  className="py-1 mr-2 mb-2 flex-row items-center justify-between gap-x-[4px]"
                >
                  <View className='flex-row items-center'>
                    {preference.desc === "Allow luggage" ? (
                      <Image
                        source={require("../assets/images/luggageNew.png")}
                        className="h-[26px] w-[26px]"
                        resizeMode="contain"
                      />
                    ) : (
                      preference.desc === "Allow Smoking/Drinking" ? (
                        <Image
                          source={require("../assets/images/smokingNew.png")}
                          className="h-[26px] w-[26px]"
                          resizeMode="contain"
                        />
                      ) : (
                        preference.desc === "Allow Pets" ? (
                          <Image
                            source={require("../assets/images/petsNew.png")}
                            className="h-[26px] w-[26px]"
                            resizeMode="contain"
                          />
                        ) : (
                          <Image
                            source={require("../assets/images/bikesNew.png")}
                            className="h-[26px] w-[26px]"
                            resizeMode="contain"
                          />
                        )
                      )
                    )}
                    <Text className="text-[14px] font-medium ml-2">
                      {capitalizeFirstLetter(preference.desc.split(" ")[1])}
                    </Text>
                  </View>
                 
                  {preference.value === true ? (
                    <View className='flex-row items-center'>
                        <Image
                          source={require("../assets/images/accepted.png")}
                          className="h-[12px] w-[12px] mr-1"
                          resizeMode="contain"
                        />
                        <Text className='text-[#34A853] font-medium text-sm'>Yes</Text>
                    </View>
                    
                  ) : (
                    <View className='flex-row items-center'>
                        <Image
                          source={require("../assets/images/cancel.png")}
                          className="h-[12px] w-[12px] mr-1"
                          resizeMode="contain"
                        />
                        <Text className='text-[#787A80] font-medium text-sm'>No</Text>
                    </View>
                  )}
                </View>
              ))}
            </View>
          </View>
        )}
        {!bookingRide && <PaymentMode />}
        

        {bookingRide && 
          <View className="mt-2 bg-white rounded-md p-4">
            <View className="flex-row justify-between items-center mb-4">
              <Text className="text-[#151B2D] text-[16px] font-semibold">Payment method</Text>
              <TouchableOpacity onPress={() => setBookingRide(false)}>
                <Text className="text-[#473BF0] font-medium">Change</Text>
              </TouchableOpacity>
            </View>
            <View className="flex-row items-center gap-x-2">
              <Image
                source={
                  ride.modeOfPayment === "cash" 
                    ? require("../assets/images/cash.png")
                    : ride.modeOfPayment === "card"
                    ? require("../assets/images/bank.png")
                    : require("../assets/images/wallet.png")
                }
                className="w-[22px] h-[22px]"
                resizeMode="contain"
              />
              <Text className="text-[#151B2D] text-[14px]">
                {ride.modeOfPayment === "cash" 
                  ? "Cash"
                  : ride.modeOfPayment === "card"
                  ? "Debit card ending with XX99"
                  : "Apple Pay or GPay"}
              </Text>
            </View>
          </View>
        }

        {bookingRide && 
          <View className='mt-2 p-4 rounded-md bg-white'>
            <Text className='text-[13px] font-semibold mb-4'>Payment</Text>
            <View>
              <View className='flex-row justify-between mb-4'>
                <Text className='text-sm font-medium text-[#787A80]'>Fare</Text>
                <Text className='text-sm font-medium'>N{trip.pricePerSeat}</Text>
              </View>
              <View className='flex-row justify-between mb-4'>
                <Text className='text-sm font-medium text-[#787A80]'>Booking fee</Text>
                <Text className='text-sm font-medium'>N 0</Text>
              </View>
              <View className='flex-row justify-between mb-4'>
                <Text className='text-sm font-medium text-[#473BF0]'>Discount</Text>
                <Text className='text-sm font-medium text-[#473BF0]'>N0</Text>
              </View>
              <View className='flex-row justify-between mb-4'>
                <Text className='text-base font-semibold'>Total</Text>
                <Text className='text-base font-semibold'>N{trip.pricePerSeat + 0 + 0}</Text>
              </View>
            </View>
          </View>
        }

        <InteractiveTripMap trip={trip} />

        {/* <View className=" mt-4 bg-white rounded-lg p-4 shadow-sm">
          <Text className="text-[#151B2D] font-semibold text-xl mb-3">Trip Details</Text>
          <View className="space-y-3">
            <View className="flex-row justify-between">
              <Text className="text-[#787A80]">Departure Time</Text>
              <Text className="text-[#151B2D] font-medium">{extractTimeFromISO(trip.timestamp)}</Text>
            </View>
            <View className="flex-row justify-between">
              <Text className="text-[#787A80]">Price per Seat</Text>
              <Text className="text-[#473BF0] font-semibold">${trip.pricePerSeat}</Text>
            </View>
            <View className="flex-row justify-between">
              <Text className="text-[#787A80]">Available Seats</Text>
              <Text className="text-[#151B2D] font-medium">{trip.noOfPassengers}</Text>
            </View>
          </View>
        </View> */}

        
      </ScrollView>

      {/* Bottom Button */}
      <View className="p-4 bg-white flex-row items-center justify-around">
        {bookingRide && 
        <>
          <Button
            text="Cancel ride"
            textClassName="text-black"
            width="w-[170px]"
            buttonClassName="bg-[#F4F4F4]"
            onClick={() => {
              setBookingRide(false);
            }}
          />

          <Button
            text="Book ride"
            textClassName="text-white"
            width="w-[170px]"
            buttonClassName="bg-[#473BF0]"
            buttonDisabled={!ride.modeOfPayment || isPending}
            isLoading={isPending}
            onClick={handleBookRide}
          />
          </>
        }
          {!bookingRide && 
          <>
            <Button
              text="Book ride"
              textClassName="text-white"
              width="w-[170px]"
              buttonClassName="bg-[#473BF0]"
              onClick={() => {
                setBookingRide(true);
                setShowPassenger(true);
              }}
            />
            <TouchableOpacity
              activeOpacity={0.8}
              onPress={() => router.push(`/${"chat"}`)}
              className="items-center justify-center"
            >
              <Image
                source={require("../assets/images/Chat.png")}
                className="w-[44px] h-[44px]"
                resizeMode="contain"
              />
            </TouchableOpacity>

            <TouchableOpacity
              onPress={() => makePhoneCall(trip?.driver?.phoneNumber)}
              activeOpacity={0.8}
              className="items-center justify-center"
            >
              <Image
                source={require("../assets/images/Call.png")}
                className="w-[44px] h-[44px]"
                resizeMode="contain"
              />
            </TouchableOpacity>
          </>
          }
        
      </View>
    </View>
  )
}

export default TripPreview