import React from 'react';
import { View, Text, TouchableOpacity, ScrollView } from 'react-native';
import { usePassengerTripEndedStore } from '@/app/store/passengerTripEndedStore';
import { passengerTripEndedListenerHelpers } from '@/utils/passengerTripEndedListener';
import { driverTripEndedPublisherHelpers } from '@/utils/driverTripEndedPublisher';
import useActiveTrip from '@/hooks/useActiveTrip';
import useCurrentUser from '@/hooks/useCurrentUser';

interface PassengerTripEndedDebuggerProps {
  ablyRef?: React.RefObject<any>;
}

const PassengerTripEndedDebugger: React.FC<PassengerTripEndedDebuggerProps> = ({ ablyRef }) => {
  const { data: user } = useCurrentUser();
  const { data: activeTrip } = useActiveTrip();
  
  const {
    showModal,
    tripData,
    isRatingTrip,
    selectedRating,
    reviewText,
  } = usePassengerTripEndedStore();

  const testTripEndedEvent = () => {
    const mockTripData = {
      tripId: activeTrip?.id || 'test_trip_123',
      driverName: 'Test Driver',
      origin: 'Test Origin',
      destination: 'Test Destination',
      cost: '$25',
      endReason: 'completed' as const,
      endTime: new Date().toISOString(),
    };

    console.log('🧪 TEST: Manually triggering trip ended modal', mockTripData);
    usePassengerTripEndedStore.getState().showTripEndedModal(mockTripData);
  };

  const testDriverPublish = async () => {
    if (!activeTrip || !user) {
      console.warn('🧪 TEST: No active trip or user for testing');
      return;
    }

    const mockTrip = {
      id: activeTrip.id,
      origin: { name: activeTrip.origin?.name || 'Test Origin' },
      destination: { name: activeTrip.destination?.name || 'Test Destination' },
      passengers: [{ id: user.id, firstName: user.firstName, lastName: user.lastName }],
      price: '$25',
    };

    const mockDriver = {
      id: activeTrip.driver?.id || 'test_driver',
      firstName: activeTrip.driver?.firstName || 'Test',
      lastName: activeTrip.driver?.lastName || 'Driver',
    };

    console.log('🧪 TEST: Manually publishing trip ended event', { mockTrip, mockDriver });
    
    try {
      await driverTripEndedPublisherHelpers.publishTripEnded(mockTrip, mockDriver, 'completed');
      console.log('✅ TEST: Trip ended event published successfully');
    } catch (error) {
      console.error('❌ TEST: Failed to publish trip ended event:', error);
    }
  };

  const getListenerStatus = () => {
    return {
      isReady: passengerTripEndedListenerHelpers.isReady(),
      listeningTrips: passengerTripEndedListenerHelpers.getListening(),
      publisherReady: driverTripEndedPublisherHelpers.isReady(),
    };
  };

  const status = getListenerStatus();

  if (!__DEV__) {
    return null; // Only show in development
  }

  return (
    <View className="bg-yellow-100 border border-yellow-400 rounded-lg p-4 m-4">
      <Text className="text-lg font-bold text-yellow-800 mb-2">
        🔍 Passenger Trip Ended Debugger
      </Text>
      
      <ScrollView className="max-h-64">
        <View className="mb-4">
          <Text className="font-semibold text-yellow-800">Modal State:</Text>
          <Text className="text-sm text-yellow-700">
            • Show Modal: {showModal ? '✅ TRUE' : '❌ FALSE'}
          </Text>
          <Text className="text-sm text-yellow-700">
            • Has Trip Data: {tripData ? '✅ YES' : '❌ NO'}
          </Text>
          <Text className="text-sm text-yellow-700">
            • Trip ID: {tripData?.tripId || 'N/A'}
          </Text>
          <Text className="text-sm text-yellow-700">
            • Is Rating: {isRatingTrip ? '✅ YES' : '❌ NO'}
          </Text>
        </View>

        <View className="mb-4">
          <Text className="font-semibold text-yellow-800">Listener Status:</Text>
          <Text className="text-sm text-yellow-700">
            • Listener Ready: {status.isReady ? '✅ YES' : '❌ NO'}
          </Text>
          <Text className="text-sm text-yellow-700">
            • Listening Trips: {status.listeningTrips.join(', ') || 'None'}
          </Text>
          <Text className="text-sm text-yellow-700">
            • Publisher Ready: {status.publisherReady ? '✅ YES' : '❌ NO'}
          </Text>
        </View>

        <View className="mb-4">
          <Text className="font-semibold text-yellow-800">Active Trip:</Text>
          <Text className="text-sm text-yellow-700">
            • Has Active Trip: {activeTrip ? '✅ YES' : '❌ NO'}
          </Text>
          <Text className="text-sm text-yellow-700">
            • Trip ID: {activeTrip?.id || 'N/A'}
          </Text>
          <Text className="text-sm text-yellow-700">
            • User Role: {activeTrip?.driver?.id === user?.id ? 'Driver' : 'Passenger'}
          </Text>
          <Text className="text-sm text-yellow-700">
            • Trip Status: {activeTrip?.status || 'N/A'}
          </Text>
        </View>

        <View className="mb-4">
          <Text className="font-semibold text-yellow-800">User Info:</Text>
          <Text className="text-sm text-yellow-700">
            • User ID: {user?.id || 'N/A'}
          </Text>
          <Text className="text-sm text-yellow-700">
            • User Name: {user?.firstName} {user?.lastName}
          </Text>
        </View>
      </ScrollView>

      <View className="flex-row flex-wrap gap-2 mt-4">
        <TouchableOpacity
          onPress={testTripEndedEvent}
          className="bg-blue-500 px-3 py-2 rounded"
        >
          <Text className="text-white text-xs">Test Modal</Text>
        </TouchableOpacity>

        <TouchableOpacity
          onPress={testDriverPublish}
          className="bg-green-500 px-3 py-2 rounded"
        >
          <Text className="text-white text-xs">Test Publish</Text>
        </TouchableOpacity>

        <TouchableOpacity
          onPress={() => {
            console.log('🔍 CURRENT STATUS:', {
              modalState: { showModal, tripData, isRatingTrip },
              listenerStatus: status,
              activeTrip: activeTrip ? { id: activeTrip.id, status: activeTrip.status } : null,
              user: user ? { id: user.id, name: `${user.firstName} ${user.lastName}` } : null,
              timestamp: new Date().toISOString()
            });
          }}
          className="bg-purple-500 px-3 py-2 rounded"
        >
          <Text className="text-white text-xs">Log Status</Text>
        </TouchableOpacity>

        <TouchableOpacity
          onPress={() => {
            if (user?.id && activeTrip?.id) {
              console.log('🧪 TEST: Manually initializing listener', {
                userId: user.id,
                tripId: activeTrip.id
              });

              // Try to get Ably from props first, then fallback to global scope
              const ably = ablyRef?.current || (global as any).ablyInstance || (window as any).ablyInstance;

              console.log('🔍 DEBUG: Ably sources check', {
                fromProps: !!ablyRef?.current,
                fromGlobal: !!(global as any).ablyInstance,
                fromWindow: !!(window as any).ablyInstance,
                finalAbly: !!ably,
                ablyConnectionState: ably?.connection?.state
              });

              if (ably) {
                passengerTripEndedListenerHelpers.initialize(ably, user.id);
                passengerTripEndedListenerHelpers.startListening(activeTrip.id);
                console.log('✅ TEST: Manual initialization completed');
              } else {
                console.warn('❌ TEST: No Ably instance found for manual init');
              }
            } else {
              console.warn('❌ TEST: Missing user or active trip for manual init');
            }
          }}
          className="bg-orange-500 px-3 py-2 rounded"
        >
          <Text className="text-white text-xs">Init Listener</Text>
        </TouchableOpacity>

        <TouchableOpacity
          onPress={() => {
            usePassengerTripEndedStore.getState().resetState();
            console.log('🧹 DEBUG: Reset modal state');
          }}
          className="bg-red-500 px-3 py-2 rounded"
        >
          <Text className="text-white text-xs">Reset</Text>
        </TouchableOpacity>
      </View>
    </View>
  );
};

export default PassengerTripEndedDebugger;
