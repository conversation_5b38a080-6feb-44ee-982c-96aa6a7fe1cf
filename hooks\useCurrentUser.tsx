import { services } from "@/services";
import { useQuery } from "@tanstack/react-query";

const useCurrentUser = () => {
  const { data, isLoading, error, refetch } = useQuery({
    queryKey: ["user"],
    queryFn: () => services.getCurrentUser(),
    staleTime: 5 * 60 * 1000, // 5 minutes - prevent unnecessary refetches
    gcTime: 10 * 60 * 1000, // 10 minutes cache time
    refetchOnWindowFocus: false, // Don't refetch when window regains focus
    refetchOnMount: false, // Don't refetch on component mount if data exists
    retry: 1, // Only retry once on failure
  });

  return { data, isLoading, error, refetch };
};

export default useCurrentUser;
