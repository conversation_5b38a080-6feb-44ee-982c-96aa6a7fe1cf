import React, { useRef } from 'react';
import { View, Text, TouchableOpacity } from 'react-native';
import { router } from 'expo-router';
import { useMutation } from '@tanstack/react-query';
import { services } from '@/services';
import Toast from 'react-native-toast-message';
import { useTripStore } from '@/app/store/tripStore';
import { useRide } from '@/context/RideProvider';
import { driverTripEndedPublisherHelpers } from '@/utils/driverTripEndedPublisher';
import CustomModal from '@/components/Modal';
import Button from '@/components/Button';

interface CancelTripModalProps {
  trip: any;
  currentUser: any;
  isDriver: boolean;
  onTripCancelled?: () => void;
}

const useCancelTripModal = ({
  trip,
  currentUser,
  isDriver,
  onTripCancelled
}: CancelTripModalProps) => {
  const cancelTripModalRef = useRef<any>(null);
  const { setIsEnabled } = useRide();

  const {
    showCancelTripModal,
    setShowCancelTripModal,
    showPassengerCancelTripModal,
    setShowPassengerCancelTripModal,
  } = useTripStore();

  const { mutate: cancelTrip, isPending: isCancellingTrip } = useMutation({
    mutationFn: services.cancelTrip,
    onSuccess: async (data) => {
      Toast.show({
        type: "success",
        text1: data?.message || "Trip cancelled successfully",
      });
      
      setShowCancelTripModal(false);
      setIsEnabled(false);

      // Publish Ably event for passenger trip cancelled modal
      if (trip && currentUser && isDriver) {
        try {
          // Convert trip to the expected format for the publisher
          const tripForPublisher = {
            ...trip,
            id: trip.id,
            passengers: trip.passengers || []
          };

          await driverTripEndedPublisherHelpers.publishTripCancelled(
            tripForPublisher,
            {
              id: currentUser.id,
              firstName: currentUser.firstName,
              lastName: currentUser.lastName,
              profilePicture: currentUser.profilePicture
            },
            'Trip cancelled by driver'
          );
          console.log('📡 Published trip-cancelled event to passengers');
        } catch (error) {
          console.error('❌ Failed to publish trip-cancelled event:', error);
        }
      }

      // Call the callback if provided
      if (onTripCancelled) {
        onTripCancelled();
      }

      // Navigate to home
      router.push("/(tabs)/Home");
    },
    onError: (error: any) => {
      Toast.show({
        type: "error",
        text1: error?.response?.data?.message || "Failed to cancel trip",
      });
    },
  });

  const handleCancelTrip = () => {
    console.log('Cancel Trip Pressed');
    setShowCancelTripModal(true);
    setTimeout(() => {
      cancelTripModalRef.current?.expand();
    }, 100);
  };

  const handleConfirmCancel = () => {
    if (trip?.id) {
      cancelTrip(trip.id);
    }
  };

  const renderDriverModals = () => (
    <>
      {showCancelTripModal && (
        <CustomModal
          ref={cancelTripModalRef}
          index={0}
          customSnapPoints={["25%"]}
        >
          <View className="flex-1 items-center justify-center w-[90%] mx-auto">
            <Text className='text-[40px] mb-[6px]'>⚠️</Text>
            <Text className="text-xl font-semibold mb-4 text-center">
              Cancel Trip?
            </Text>
            <Text className="text-center text-[15px] font-normal mb-6">
              Are you sure you want to cancel this trip? This action cannot be undone.
            </Text>
            <View className="flex-row w-full space-x-3">
              <Button
                text="Keep Trip"
                buttonClassName="bg-[#F4F4F4] flex-1"
                textClassName="text-black"
                onClick={() => {
                  setShowCancelTripModal(false);
                  cancelTripModalRef.current?.close();
                }}
              />
              <Button
                text="Cancel Trip"
                buttonClassName="bg-red-500 flex-1"
                textClassName="text-white"
                onClick={handleConfirmCancel}
                isLoading={isCancellingTrip}
                buttonDisabled={isCancellingTrip}
              />
            </View>
          </View>
        </CustomModal>
      )}
    </>
  );

  const renderPassengerModals = () => (
    <>
      {showPassengerCancelTripModal && (
        <CustomModal
          ref={cancelTripModalRef}
          index={0}
          customSnapPoints={["35%"]}
        >
          <View className="flex-1 items-center justify-center w-[90%] mx-auto">
            <Text className='text-[40px] mb-[6px]'>😔</Text>
            <Text className="text-xl font-semibold mb-4 text-center">
              Trip Cancelled
            </Text>
            <Text className="text-center text-[15px] font-normal mb-6">
              The driver has cancelled this trip. We apologize for the inconvenience.
            </Text>
            <Button
              text="Okay"
              buttonClassName="bg-[#F4F4F4] mb-3"
              textClassName="text-black"
              onClick={() => {
                setShowPassengerCancelTripModal(false);
                router.replace("/(tabs)/Home");
              }}
            />
          </View>
        </CustomModal>
      )}
    </>
  );

  // Export the handleCancelTrip function and render method
  return {
    handleCancelTrip,
    renderModals: () => (
      <>
        {isDriver ? renderDriverModals() : renderPassengerModals()}
      </>
    ),
  };
};

export default useCancelTripModal;
