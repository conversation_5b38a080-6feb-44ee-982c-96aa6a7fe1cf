import {
  View,
  SafeAreaView,
  TouchableWithoutFeedback,
  KeyboardAvoidingView,
  Platform,
  Keyboard,
  StatusBar,
} from "react-native";
import React from "react";
import { router } from "expo-router";
import AuthHeader from "@/components/AuthHeader";
import Button from "@/components/Button";
import Input from "@/components/Input";
import { useUser } from "@/context/UserContext";
import { useMutation } from "@tanstack/react-query";
import { services } from "@/services";
import Toast from "react-native-toast-message";

const PersonalInfo = () => {
  const { userInfo, setUserDetails } = useUser();

  const { mutate: SignUp, isPending: SignUpPending } = useMutation({
    mutationFn: services.signUp,
    onSuccess: () => {
      Toast.show({
        type: "success",
        text1: "Account Created!",
      });
      router.replace("/SignIn");
    },
    onError: (error: any) => {
      Toast.show({
        type: "error",
        text1: error.response
          ? error.response.data.description || error.response.data.message
          : error.message,
      });
    },
  });
  return (
    <KeyboardAvoidingView
      behavior={Platform.OS === "ios" ? "padding" : "height"}
      className="flex-1"
    >
      <TouchableWithoutFeedback onPress={Keyboard.dismiss}>
        <SafeAreaView className="h-full bg-white flex-1 w-full">
          <StatusBar barStyle="dark-content" backgroundColor={"transparent"} />
          <View className="w-[90%] mx-auto h-full">
            <AuthHeader
              header="Enter your name"
              subHeader="Please enter your name as it appears on your ID or passport"
              subbHeader=""
            />

            <View className="mt-10">
              <Input
                text="First name"
                type="text"
                placeHolder="John"
                value={userInfo.firstName}
                setValue={setUserDetails}
                ObjKey="firstName"
              />
              <View className="h-3" />
              <Input
                text="Last name"
                type="text"
                placeHolder="Doe"
                value={userInfo.lastName}
                setValue={setUserDetails}
                ObjKey="lastName"
              />
            </View>

            <View className="absolute bottom-5 w-full">
              <Button
                isLoading={SignUpPending}
                buttonDisabled={!userInfo.firstName || !userInfo.lastName}
                text="Continue"
                buttonClassName="bg-[#473BF0]"
                textClassName="text-white"
                onClick={() =>
                  SignUp({
                    dataBody: {
                      firstName: userInfo.firstName,
                      lastName: userInfo.lastName,
                      email: userInfo.email,
                      phoneNumber: userInfo.phoneNumber,
                      password: userInfo.password,
                      hasPersonalVehicle: userInfo.ownPersonalVehicle,
                    },
                  })
                }
              />
            </View>
          </View>
        </SafeAreaView>
      </TouchableWithoutFeedback>
    </KeyboardAvoidingView>
  );
};

export default PersonalInfo;
