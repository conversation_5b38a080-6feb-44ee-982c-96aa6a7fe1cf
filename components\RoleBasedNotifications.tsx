import React, { useEffect, useRef, useState, forwardRef, useImperativeHandle } from 'react';
import { View, Text } from 'react-native';
import Ably, { RealtimeChannel } from 'ably';
import useCurrentUser from '@/hooks/useCurrentUser';
import useActiveTrip from '@/hooks/useActiveTrip';
import useGetAblyToken from '@/hooks/useGetAblyToken';
import RideRequestNotification from './RideRequestNotification';
import BookingStatusNotification from './BookingStatusNotification';
import TripStartedNotification from './TripStartedNotification';
import PickupConfirmedNotification from './PickupConfirmedNotification';
import { services } from '@/services';
import { useMutation, useQuery } from '@tanstack/react-query';
import Toast from 'react-native-toast-message';
import { queryClient } from '@/providers';
import { useNotificationStore } from '@/app/store/notificationStore';
import { useTripStore } from '@/app/store/tripStore';
import { realtimeNotificationService } from '@/utils/realtimeNotifications';

interface RideRequest {
  id: string;
  firstName: string;
  lastName: string;
  pickup: {
    name: string;
    lat: number;
    lng: number;
  };
  dropoff: {
    name: string;
    lat: number;
    lng: number;
  };
  requestedAt: string;
  modeOfPayment: string;
}

interface BookingStatus {
  status: 'accepted' | 'declined';
  tripDetails: {
    id: string;
    driverName: string;
    origin: {
      name: string;
      lat: number;
      lng: number;
    };
    destination: {
      name: string;
      lat: number;
      lng: number;
    };
    timestamp: string;
    pricePerSeat: number;
  };
  fullTripData?: any; // Complete trip object for navigation
  timestamp: string;
}

interface TripDetails {
  id: string;
  driverName?: string;
  origin: {
    name: string;
    lat: number;
    lng: number;
  };
  destination: {
    name: string;
    lat: number;
    lng: number;
  };
  timestamp: string;
  pricePerSeat?: number;
  passengers?: Array<{
    firstName: string;
    lastName: string;
  }>;
}

interface TripStarted {
  tripDetails: TripDetails;
  timestamp: string;
}

// Define the ref interface for external refresh
export interface RoleBasedNotificationsRef {
  refresh: () => Promise<void>;
  cleanup: () => void;
}

// Define props interface for auto-refresh callback
interface RoleBasedNotificationsProps {
  onAutoRefresh?: () => Promise<void>;
}

const RoleBasedNotifications = forwardRef<RoleBasedNotificationsRef, RoleBasedNotificationsProps>((props, ref) => {
  const { onAutoRefresh } = props;
  const { data: currentUser } = useCurrentUser();
  const { data: activeTrip } = useActiveTrip();
  const { ablyToken } = useGetAblyToken();
  const { setUserContext } = useNotificationStore();

  const ablyRef = useRef<Ably.Realtime | null>(null);
  const channelRef = useRef<RealtimeChannel | null>(null);

  const [rideRequests, setRideRequests] = useState<RideRequest[]>([]);
  const [bookingStatus, setBookingStatus] = useState<BookingStatus | null>(null);
  const [tripStarted, setTripStarted] = useState<TripStarted | null>(null);
  const [pickupConfirmed, setPickupConfirmed] = useState<{
    tripDetails: TripDetails;
    passengerName?: string;
    timestamp: string;
  } | null>(null);

  // Determine user role
  const isDriver = activeTrip?.driver?.id === currentUser?.id;
  const userRole = isDriver ? 'driver' : 'passenger';

  // Debug logging for role determination
  useEffect(() => {
    if (currentUser && activeTrip) {
      console.log('🔍 Role Debug Info:', {
        currentUserId: currentUser.id,
        driverId: activeTrip.driver?.id,
        isDriver,
        userRole,
        tripId: activeTrip.id,
        tripStatus: activeTrip.status,
        hasBookingStatus: !!bookingStatus,
        bookingStatusValue: bookingStatus
      });
    }
  }, [currentUser?.id, activeTrip?.driver?.id, isDriver, userRole, activeTrip?.id]);

  // Fetch existing ride requests from API (for drivers)
  const { data: apiRideRequests, refetch: refetchRequests, isLoading: isLoadingRequests } = useQuery({
    queryKey: ["requests", activeTrip?.id],
    queryFn: () => services.getCarpoolRequests("carpool", activeTrip?.id),
    enabled: isDriver && !!activeTrip?.id,
    staleTime: 30000, // Consider data fresh for 30 seconds
    refetchOnWindowFocus: false, // Don't refetch when window regains focus
  });

  // Use existing stores for persistence (like requests and active trips)
  const { requestStatuses } = useTripStore();
  const { notifications, addNotification, markNotificationRead } = useNotificationStore();

  // Expose refresh and cleanup functions via ref
  useImperativeHandle(ref, () => ({
    refresh: async () => {
      console.log('🔄 RoleBasedNotifications: Manual refresh triggered');
      try {
        // Refetch ride requests if user is a driver
        if (isDriver && activeTrip?.id) {
          await refetchRequests();
        }

        // Invalidate related queries to ensure fresh data
        await queryClient.invalidateQueries({ queryKey: ["requests"] });
        await queryClient.invalidateQueries({ queryKey: ["trip"] });

        console.log('✅ RoleBasedNotifications: Refresh completed');
      } catch (error) {
        console.error('❌ RoleBasedNotifications: Refresh failed:', error);
      }
    },
    cleanup: () => {
      console.log('🧹 RoleBasedNotifications: Manual cleanup triggered');

      // Clear all notification states
      setTripStarted(null);
      setBookingStatus(null);
      setPickupConfirmed(null);

      // Mark all unread notifications as read
      const unreadNotifications = notifications.filter(notif => !notif.read);
      unreadNotifications.forEach(notif => markNotificationRead(notif.id));
    }
  }), [isDriver, activeTrip?.id, refetchRequests, notifications, markNotificationRead]);

  // Set user context in notification store
  useEffect(() => {
    if (currentUser && activeTrip) {
      setUserContext(userRole, activeTrip.id, currentUser.id);
    }
  }, [currentUser?.id, activeTrip?.id, userRole, setUserContext]);

  // Auto-refresh when trip data changes (similar to ActiveRides)
  useEffect(() => {
    if (isDriver && activeTrip?.id) {
      console.log('🔄 RoleBasedNotifications: Trip data changed, refreshing requests');
      refetchRequests();
    }
  }, [activeTrip?.id, isDriver, refetchRequests]);

  // Restore notification states from persisted data (like requests and active trips)
  useEffect(() => {
    if (!activeTrip?.id || !currentUser?.id) return;

    console.log('🔄 RoleBasedNotifications: Restoring notification states from persisted data');

    // Check for pickup confirmed status from trip store
    const currentTripRequests = Object.entries(requestStatuses).filter(([requestId, status]) =>
      status.pickupConfirmed === true
    );

    if (currentTripRequests.length > 0 && !isDriver) {
      // Show pickup confirmed notification for passenger if any request has pickup confirmed
      const [requestId, status] = currentTripRequests[0]; // Show for the first confirmed pickup
      setPickupConfirmed({
        tripDetails: {
          id: activeTrip.id,
          driverName: activeTrip.driver?.firstName || 'Driver',
          origin: activeTrip.origin || { name: 'Unknown', lat: 0, lng: 0 },
          destination: activeTrip.destination || { name: 'Unknown', lat: 0, lng: 0 },
          timestamp: activeTrip.timestamp || new Date().toISOString(),
          pricePerSeat: activeTrip.pricePerSeat,
        },
        passengerName: currentUser.firstName,
        timestamp: new Date().toISOString(),
      });
    }

    // Note: Trip started notifications should only be triggered by real-time events
    // when the driver explicitly starts the trip, not based on trip status alone.
    // Trip status 'ongoing'/'in_progress' means the trip is booked/confirmed,
    // but doesn't mean the driver has started the trip yet.

    // Check for persisted notifications from notification store
    const recentTripStartedNotifications = notifications.filter(notif =>
      notif.tripId === activeTrip.id &&
      notif.type === 'trip_started' &&
      !notif.read
    );

    const recentPickupConfirmedNotifications = notifications.filter(notif =>
      notif.tripId === activeTrip.id &&
      notif.type === 'pickup_confirmed' &&
      notif.targetRole === 'passenger' &&
      !notif.read
    );

    // Restore trip started notification if found
    if (recentTripStartedNotifications.length > 0) {
      const latestNotification = recentTripStartedNotifications[0];
      if (latestNotification.data) {
        console.log('🔄 Restoring trip started notification from store');
        setTripStarted(latestNotification.data);
      }
    }

    // Restore pickup confirmed notification if found (passenger only)
    if (recentPickupConfirmedNotifications.length > 0 && !isDriver) {
      const latestNotification = recentPickupConfirmedNotifications[0];
      if (latestNotification.data) {
        console.log('🔄 Restoring pickup confirmed notification from store');
        setPickupConfirmed(latestNotification.data);
      }
    }

  }, [activeTrip?.id, currentUser?.id, requestStatuses, notifications, isDriver, activeTrip]);

  // Clear booking status only when trip is actually ended (not just missing from refresh)
  useEffect(() => {
    if (activeTrip?.data && (
      activeTrip.data.status === 'completed' ||
      activeTrip.data.status === 'cancelled' ||
      activeTrip.data.status === 'ended'
    )) {
      if (bookingStatus) {
        console.log('🧹 Clearing booking status - trip actually ended with status:', activeTrip.data.status);
        setBookingStatus(null);
      }
    }
  }, [activeTrip?.data?.status, bookingStatus]);

  // Auto-cleanup notifications when trip ends or becomes inactive
  useEffect(() => {
    const shouldCleanup = !activeTrip?.id ||
                         activeTrip.status === 'completed' ||
                         activeTrip.status === 'cancelled' ||
                         activeTrip.status === 'ended';

    if (shouldCleanup) {
      console.log('🧹 RoleBasedNotifications: Trip ended/inactive, cleaning up notifications', {
        hasActiveTrip: !!activeTrip?.id,
        tripStatus: activeTrip?.status,
        isDriver,
        userRole
      });

      // Clear trip-related notification states
      setTripStarted(null);
      setPickupConfirmed(null);

      // Clear booking status only when trip is actually ended (preserve during refreshes)
      if (activeTrip?.id && (activeTrip.status === 'completed' || activeTrip.status === 'cancelled' || activeTrip.status === 'ended')) {
        console.log('🧹 Clearing booking status - trip actually ended');
        setBookingStatus(null);
      } else if (!activeTrip?.id && isDriver) {
        // Clear for drivers when no active trip (they don't need persistent booking status)
        console.log('🧹 Clearing booking status - driver with no active trip');
        setBookingStatus(null);
      } else {
        console.log('🧹 Preserving booking status during refresh - passenger without confirmed trip end');
      }

      // Mark all unread notifications as read (for any trip, since there's no active trip)
      const unreadNotifications = notifications.filter(notif => !notif.read);

      unreadNotifications.forEach(notif => {
        console.log(`🧹 Marking notification as read: ${notif.type} - ${notif.title} (Trip: ${notif.tripId})`);
        markNotificationRead(notif.id);
      });
    }
  }, [activeTrip?.id, activeTrip?.status, notifications, markNotificationRead]);

  // Helper function to trigger auto-refresh of parent component
  const triggerAutoRefresh = async () => {
    console.log('🔄 RoleBasedNotifications: Triggering auto-refresh');
    try {
      if (onAutoRefresh) {
        await onAutoRefresh();
      }
    } catch (error) {
      console.error('❌ RoleBasedNotifications: Auto-refresh failed:', error);
    }
  };

  // Load existing ride requests from API when component mounts
  useEffect(() => {
    if (isDriver && apiRideRequests?.data) {
      console.log('📋 Loading existing ride requests from API:', apiRideRequests.data);

      // Filter out requests without proper passenger names and only include valid requests
      const validRequests = apiRideRequests.data.filter((request: any) =>
        request.firstName && request.firstName !== 'Unknown' &&
        request.lastName && request.lastName !== 'User'
      );

      const existingRequests: RideRequest[] = validRequests.map((request: any) => ({
        id: request.requestId || request.id,
        firstName: request.firstName,
        lastName: request.lastName,
        pickup: request.pickup,
        dropoff: request.dropoff,
        requestedAt: request.requestedAt || new Date().toISOString(),
        modeOfPayment: request.modeOfPayment || 'Cash',
      }));

      console.log('📋 Processed valid requests:', existingRequests);
      setRideRequests(existingRequests);
    }
  }, [isDriver, apiRideRequests?.data]);

  // Accept ride request mutation
  const { mutate: acceptRideRequest } = useMutation({
    mutationFn: services.acceptRideRequest,
    onSuccess: (data) => {
      Toast.show({
        type: "success",
        text1: data.message,
      });
      // Remove the accepted request from the list
      setRideRequests(prev => prev.filter(req => req.id !== data.requestId));
      queryClient.invalidateQueries({ queryKey: ["activeTrip"] });
      queryClient.invalidateQueries({ queryKey: ["requests"] });
      refetchRequests(); // Refetch API data

      // Trigger auto-refresh of parent component
      triggerAutoRefresh();
    },
    onError: (error: any) => {
      Toast.show({
        type: "error",
        text1: error.response?.data?.message || "Failed to accept ride request",
      });
    },
  });

  // Reject ride request mutation
  const { mutate: rejectRideRequest } = useMutation({
    mutationFn: services.rejectRideRequest,
    onSuccess: (data) => {
      Toast.show({
        type: "success",
        text1: data.message,
      });
      // Remove the rejected request from the list
      setRideRequests(prev => prev.filter(req => req.id !== data.requestId));
      queryClient.invalidateQueries({ queryKey: ["activeTrip"] });
      queryClient.invalidateQueries({ queryKey: ["requests"] });
      refetchRequests(); // Refetch API data

      // Trigger auto-refresh of parent component
      triggerAutoRefresh();
    },
    onError: (error: any) => {
      Toast.show({
        type: "error",
        text1: error.response?.data?.message || "Failed to reject ride request",
      });
    },
  });

  // Initialize Ably connection
  useEffect(() => {
    if (!ablyToken?.clientId || !currentUser?.id) {
      console.log('RoleBasedNotifications: Missing required data for Ably initialization');
      return;
    }

    const initializeAbly = async () => {
      try {
        console.log('RoleBasedNotifications: Initializing Ably connection', {
          userId: currentUser.id,
          tripId: activeTrip?.id,
          userRole,
        });

        ablyRef.current = new Ably.Realtime({
          key: process.env.EXPO_PUBLIC_ABLY_KEY as string,
          clientId: currentUser.id,
          autoConnect: true,
        });

        // Initialize realtime notification service with correct role
        console.log('🔧 RoleBasedNotifications: Initializing notification service with role:', userRole);
        console.log('🔧 Role determination details:', {
          currentUserId: currentUser.id,
          driverId: activeTrip?.driver?.id,
          isDriver,
          userRole,
          activeTrip: activeTrip?.id
        });

        // For passengers without active trip, we'll use a generic trip ID for the service
        // The actual listening will be handled separately
        const tripIdForService = activeTrip?.id || 'passenger-notifications';
        realtimeNotificationService.initialize(
          ablyRef.current,
          tripIdForService,
          currentUser.id,
          userRole
        );

        // Set up role-specific listeners
        if (isDriver && activeTrip?.id) {
          console.log('RoleBasedNotifications: Setting up driver listeners');
          setupDriverListeners(activeTrip.id);
        } else if (!isDriver) {
          console.log('RoleBasedNotifications: Setting up passenger listeners');
          console.log('🔍 Passenger setup debug:', {
            hasActiveTrip: !!activeTrip?.id,
            activeTripId: activeTrip?.id,
            currentUserId: currentUser?.id,
            userRole
          });
          if (activeTrip?.id) {
            // Passenger with active trip - listen to that trip's channel
            console.log('🎧 Passenger has active trip, setting up trip-specific listeners');
            setupPassengerListeners(activeTrip.id);
          } else {
            // Passenger without active trip - set up global passenger notifications
            console.log('🌐 Passenger has no active trip, setting up global listeners');
            setupGlobalPassengerListeners();
          }
        }

      } catch (error) {
        console.error('RoleBasedNotifications: Failed to initialize Ably:', error);
      }
    };

    initializeAbly();

    return () => {
      console.log('RoleBasedNotifications: Cleaning up Ably connections and notifications');

      // Clean up Ably connections
      if (channelRef.current) {
        channelRef.current.unsubscribe();
      }
      if (ablyRef.current) {
        ablyRef.current.close();
      }

      // Only clean up notification states on actual unmount, not on re-initialization
      // The booking status should persist during transitions (e.g., passenger getting active trip)
      console.log('RoleBasedNotifications: Preserving notification states during re-initialization');
    };
  }, [ablyToken?.clientId, currentUser?.id, activeTrip?.id, isDriver]);

  const setupDriverListeners = (tripId: string) => {
    if (!ablyRef.current) return;

    // Listen for ride requests
    const driversChannel = ablyRef.current.channels.get("drivers");
    driversChannel.subscribe("ride-request", (message) => {
      console.log('📱 Driver received ride request:', message.data);
      console.log('📱 Message data details:', {
        firstName: message.data.firstName,
        lastName: message.data.lastName,
        tripId: message.data.tripId,
        expectedTripId: tripId
      });



      if (message.data.tripId === tripId) {
        // Only process requests with valid passenger names
        if (!message.data.firstName || !message.data.lastName ||
            message.data.firstName === 'Unknown' || message.data.lastName === 'User') {
          console.log('📱 Skipping request with invalid passenger name:', message.data);
          return;
        }

        const newRequest: RideRequest = {
          id: message.data.requestId,
          firstName: message.data.firstName,
          lastName: message.data.lastName,
          pickup: message.data.pickup,
          dropoff: message.data.dropoff,
          requestedAt: message.data.requestedAt,
          modeOfPayment: message.data.modeOfPayment,
        };

        console.log('📱 Created request object:', newRequest);

        setRideRequests(prev => {
          // Avoid duplicates
          if (prev.some(req => req.id === newRequest.id)) return prev;
          return [newRequest, ...prev];
        });

        // Trigger auto-refresh of parent component
        triggerAutoRefresh();
      }
    });

    // Listen for trip started notifications via direct event (like driver notifications)
    const tripChannel = ablyRef.current.channels.get(`trip-${tripId}`);
    tripChannel.subscribe("trip-started", (message) => {
      console.log('📱 Driver received trip started notification:', message.data);

      setTripStarted({
        tripDetails: message.data.tripDetails || {
          id: message.data.tripId || tripId,
          driverName: message.data.driverName || 'Driver',
          timestamp: message.data.timestamp || new Date().toISOString(),
          origin: activeTrip?.origin || { name: 'Unknown', lat: 0, lng: 0 },
          destination: activeTrip?.destination || { name: 'Unknown', lat: 0, lng: 0 }
        },
        timestamp: message.data.timestamp || new Date().toISOString(),
      });

      // Trigger auto-refresh of parent component
      triggerAutoRefresh();
    });

    channelRef.current = driversChannel;
  };

  const setupPassengerListeners = (tripId: string) => {
    if (!ablyRef.current) return;

    console.log('🎧 Setting up passenger listeners for trip:', tripId);
    console.log('🎧 Current user role:', userRole, 'isDriver:', isDriver);

    // Listen for booking status updates via direct events (like driver notifications)
    const tripChannelName = `trip-${tripId}`;
    const tripChannel = ablyRef.current.channels.get(tripChannelName);

    console.log('🎧 Passenger subscribing to channel:', tripChannelName);
    console.log('🎧 Ably connection state:', ablyRef.current.connection.state);

    // Wait for connection to be established before subscribing
    const setupSubscriptions = () => {
      console.log('🎧 Connection ready, setting up subscriptions for:', tripChannelName);

      // Listen to all messages for debugging (commented out to reduce log noise)
      // tripChannel.subscribe((message) => {
      //   console.log('📱 Passenger received ANY message on trip channel:', message.name);
      // });

      // Set up specific event listeners
      setupEventListeners();
    };

    const setupEventListeners = () => {
      // Listen to real-time notification service events
      tripChannel.subscribe("notification", (message) => {
        console.log('📱 Received notification from real-time service:', message.data);

        if (message.data.type === 'booking_accepted') {
          const notificationData = message.data.data;
          const bookingData = {
            status: 'accepted' as const,
            tripDetails: notificationData.tripDetails || {
              id: message.data.tripId || tripId,
              driverName: message.data.fromUserName || 'Driver',
              timestamp: message.data.timestamp || new Date().toISOString(),
              origin: activeTrip?.origin || { name: 'Unknown', lat: 0, lng: 0 },
              destination: activeTrip?.destination || { name: 'Unknown', lat: 0, lng: 0 },
              pricePerSeat: activeTrip?.pricePerSeat || 0
            },
            fullTripData: notificationData.fullTripData || activeTrip,
            timestamp: message.data.timestamp || new Date().toISOString(),
          };
          setBookingStatus(bookingData);
          triggerAutoRefresh();
        } else if (message.data.type === 'booking_declined') {
          const notificationData = message.data.data;
          const bookingData = {
            status: 'declined' as const,
            tripDetails: notificationData.tripDetails || {
              id: message.data.tripId || tripId,
              driverName: message.data.fromUserName || 'Driver',
              timestamp: message.data.timestamp || new Date().toISOString(),
              origin: activeTrip?.origin || { name: 'Unknown', lat: 0, lng: 0 },
              destination: activeTrip?.destination || { name: 'Unknown', lat: 0, lng: 0 },
              pricePerSeat: activeTrip?.pricePerSeat || 0
            },
            fullTripData: notificationData.fullTripData || activeTrip,
            timestamp: message.data.timestamp || new Date().toISOString(),
          };
          setBookingStatus(bookingData);
          triggerAutoRefresh();
        }
      });

      // Direct event listeners (like driver notifications)
      tripChannel.subscribe("booking-accepted", (message) => {
      console.log('📱 Passenger received booking accepted:', message.data);



      const bookingData = {
        status: 'accepted' as const,
        tripDetails: message.data.tripDetails || {
          id: message.data.tripId || tripId,
          driverName: message.data.driverName || 'Driver',
          timestamp: message.data.timestamp || new Date().toISOString(),
          origin: activeTrip?.origin || { name: 'Unknown', lat: 0, lng: 0 },
          destination: activeTrip?.destination || { name: 'Unknown', lat: 0, lng: 0 },
          pricePerSeat: activeTrip?.pricePerSeat || 0
        },
        fullTripData: message.data.fullTripData || activeTrip, // Include full trip data for navigation
        timestamp: message.data.timestamp || new Date().toISOString(),
      };

      setBookingStatus(bookingData);

      // Save to notification store for persistence
      addNotification({
        type: 'trip_started', // Using existing type, can be extended
        title: '✅ Booking Accepted',
        message: `Great news! Your ride request has been accepted.`,
        targetRole: 'passenger',
        tripId: tripId,
        data: bookingData,
      });

      // Trigger auto-refresh of parent component
      triggerAutoRefresh();
    });

    tripChannel.subscribe("booking-declined", (message) => {
      console.log('📱 Passenger received booking declined:', message.data);

      setBookingStatus({
        status: 'declined',
        tripDetails: message.data.tripDetails || {
          id: message.data.tripId || tripId,
          driverName: message.data.driverName || 'Driver',
          timestamp: message.data.timestamp || new Date().toISOString(),
          origin: activeTrip?.origin || { name: 'Unknown', lat: 0, lng: 0 },
          destination: activeTrip?.destination || { name: 'Unknown', lat: 0, lng: 0 },
          pricePerSeat: activeTrip?.pricePerSeat || 0
        },
        fullTripData: message.data.fullTripData || activeTrip, // Include full trip data for navigation
        timestamp: message.data.timestamp || new Date().toISOString(),
      });

      // Trigger auto-refresh of parent component
      triggerAutoRefresh();
    });

    tripChannel.subscribe("trip-started", (message) => {
      console.log('📱 Passenger received trip started notification:', message.data);
      console.log('📱 Setting trip started with data:', message.data);

      const tripStartedData = {
        tripDetails: message.data.tripDetails || {
          id: message.data.tripId || tripId,
          driverName: message.data.driverName || 'Driver',
          timestamp: message.data.timestamp || new Date().toISOString(),
          origin: activeTrip?.origin || { name: 'Unknown', lat: 0, lng: 0 },
          destination: activeTrip?.destination || { name: 'Unknown', lat: 0, lng: 0 }
        },
        timestamp: message.data.timestamp || new Date().toISOString(),
      };

      setTripStarted(tripStartedData);

      // Save to notification store for persistence
      addNotification({
        type: 'trip_started',
        title: '🚗 Trip Started',
        message: `Your trip has started. You can track the progress in real-time.`,
        targetRole: userRole === 'driver' ? 'driver' : 'passenger',
        tripId: tripId,
        data: tripStartedData,
      });

      // Trigger auto-refresh of parent component
      triggerAutoRefresh();
    });

    tripChannel.subscribe("pickup-confirmed", (message) => {
      console.log('📱 Passenger received pickup confirmed notification:', message.data);

      const pickupConfirmedData = {
        tripDetails: message.data.tripDetails || {
          id: message.data.tripId || tripId,
          driverName: message.data.driverName || 'Driver',
          timestamp: message.data.timestamp || new Date().toISOString(),
          origin: activeTrip?.origin || { name: 'Unknown', lat: 0, lng: 0 },
          destination: activeTrip?.destination || { name: 'Unknown', lat: 0, lng: 0 }
        },
        passengerName: message.data.passengerName,
        timestamp: message.data.timestamp || new Date().toISOString(),
      };

      setPickupConfirmed(pickupConfirmedData);
      console.log('✅ Pickup confirmed state set:', pickupConfirmedData);

      // Save to notification store for persistence
      addNotification({
        type: 'pickup_confirmed',
        title: '✅ Pickup Confirmed',
        message: `Great news! Your driver has confirmed your pickup.`,
        targetRole: 'passenger',
        tripId: tripId,
        data: pickupConfirmedData,
      });

      // Trigger auto-refresh of parent component
      triggerAutoRefresh();
    });

    tripChannel.subscribe("trip-ended", (message) => {
      console.log('📱 Passenger received trip ended notification:', message.data);
      console.log('🔍 Current booking status before clearing:', bookingStatus);

      // Clear booking status immediately when trip ends
      if (bookingStatus) {
        console.log('🧹 Clearing booking status - received trip-ended event');
        setBookingStatus(null);
      } else {
        console.log('🔍 No booking status to clear');
      }

      // Show cancel modal if trip was cancelled by driver
      if (message.data?.endReason === 'cancelled' && !isDriver) {
        console.log('📱 Showing passenger cancel trip modal - trip was cancelled by driver');
        const { setShowPassengerCancelTripModal } = useTripStore.getState();
        setShowPassengerCancelTripModal(true);
      } else {
        console.log('🔍 Not showing cancel modal:', {
          endReason: message.data?.endReason,
          isDriver,
          shouldShow: message.data?.endReason === 'cancelled' && !isDriver
        });
      }

      // Trigger auto-refresh to update trip state
      triggerAutoRefresh();
    });
    };

    // Check connection state and setup accordingly
    if (ablyRef.current && ablyRef.current.connection.state === 'connected') {
      console.log('🎧 Already connected, setting up subscriptions immediately');
      setupSubscriptions();
    } else if (ablyRef.current) {
      console.log('🎧 Not connected yet, waiting for connection...');
      ablyRef.current.connection.once('connected', () => {
        console.log('🎧 Connection established! Setting up subscriptions');
        setupSubscriptions();
      });
    }

    channelRef.current = tripChannel;
  };

  const setupGlobalPassengerListeners = () => {
    if (!ablyRef.current) return;

    console.log('🌐 Setting up global passenger listeners for user:', currentUser.id);

    // Listen to user-specific channel for booking notifications
    const userChannelName = `user-${currentUser.id}`;
    const userChannel = ablyRef.current.channels.get(userChannelName);

    console.log('🎧 Passenger subscribing to user channel:', userChannelName);
    console.log('🎧 Ably connection state:', ablyRef.current.connection.state);

    // Listen for booking status updates on user channel
    userChannel.subscribe("booking-accepted", (message) => {
      console.log('📱 Passenger received booking accepted on user channel:', message.data);

      const bookingData = {
        status: 'accepted' as const,
        tripDetails: message.data.tripDetails || {
          id: message.data.tripId || 'unknown',
          driverName: message.data.driverName || 'Driver',
          timestamp: message.data.timestamp || new Date().toISOString(),
          origin: message.data.origin || { name: 'Unknown', lat: 0, lng: 0 },
          destination: message.data.destination || { name: 'Unknown', lat: 0, lng: 0 },
          pricePerSeat: message.data.pricePerSeat || 0
        },
        fullTripData: message.data.fullTripData || activeTrip, // Include full trip data for navigation
        timestamp: message.data.timestamp || new Date().toISOString(),
      };

      setBookingStatus(bookingData);
      triggerAutoRefresh();
    });

    userChannel.subscribe("booking-declined", (message) => {
      console.log('📱 Passenger received booking declined on user channel:', message.data);

      setBookingStatus({
        status: 'declined',
        tripDetails: message.data.tripDetails || {
          id: message.data.tripId || 'unknown',
          driverName: message.data.driverName || 'Driver',
          timestamp: message.data.timestamp || new Date().toISOString(),
          origin: message.data.origin || { name: 'Unknown', lat: 0, lng: 0 },
          destination: message.data.destination || { name: 'Unknown', lat: 0, lng: 0 },
          pricePerSeat: message.data.pricePerSeat || 0
        },
        fullTripData: message.data.fullTripData || activeTrip, // Include full trip data for navigation
        timestamp: message.data.timestamp || new Date().toISOString(),
      });

      triggerAutoRefresh();
    });

    userChannel.subscribe("pickup-confirmed", (message) => {
      console.log('📱 Passenger received pickup confirmed on user channel:', message.data);
      console.log('🔍 User channel pickup confirmed debug:', {
        messageData: message.data,
        currentUserId: currentUser?.id,
        userChannelName
      });

      const pickupConfirmedData = {
        tripDetails: message.data.tripDetails || {
          id: message.data.tripId || 'unknown',
          driverName: message.data.driverName || 'Driver',
          timestamp: message.data.timestamp || new Date().toISOString(),
          origin: message.data.origin || { name: 'Unknown', lat: 0, lng: 0 },
          destination: message.data.destination || { name: 'Unknown', lat: 0, lng: 0 },
          pricePerSeat: message.data.pricePerSeat || 0
        },
        passengerName: message.data.passengerName,
        timestamp: message.data.timestamp || new Date().toISOString(),
      };

      console.log('✅ Setting pickup confirmed state from user channel:', pickupConfirmedData);
      setPickupConfirmed(pickupConfirmedData);
      triggerAutoRefresh();
    });

    channelRef.current = userChannel;
  };

  const handleAcceptRequest = (requestId: string) => {
    acceptRideRequest({ dataBody: { requestId } });
  };

  const handleDeclineRequest = (requestId: string) => {
    rejectRideRequest({ dataBody: { requestId } });
  };



  const handleDismissTripStarted = () => {
    setTripStarted(null);

    // Mark related notifications as read
    const tripStartedNotifications = notifications.filter(notif =>
      notif.tripId === activeTrip?.id &&
      notif.type === 'trip_started' &&
      !notif.read
    );
    tripStartedNotifications.forEach(notif => markNotificationRead(notif.id));
  };

  const handleDismissPickupConfirmed = () => {
    setPickupConfirmed(null);

    // Mark related notifications as read
    const pickupConfirmedNotifications = notifications.filter(notif =>
      notif.tripId === activeTrip?.id &&
      notif.type === 'pickup_confirmed' &&
      !notif.read
    );
    pickupConfirmedNotifications.forEach(notif => markNotificationRead(notif.id));
  };

  // Don't render anything if there's no current user
  // For passengers, they might not have activeTrip but could still receive notifications
  if (!currentUser) {
    return null;
  }

  // If no active trip, passengers can still receive booking notifications
  // Only drivers need an active trip to show ride requests
  if (!activeTrip && isDriver) {
    return __DEV__ ? (
      <View className="bg-yellow-50 border border-yellow-200 rounded-md p-4 mx-4 mb-4">
        <Text className="text-yellow-800 text-sm font-medium">
          🔍 Debug: Driver has no active trip for notifications
        </Text>
        <Text className="text-yellow-600 text-xs mt-1">
          User ID: {currentUser.id} | Role: {userRole}
        </Text>
      </View>
    ) : null;
  }

  return (
    <View className='w-full items-center justify-center'>

      {/* Trip Started Notifications (Both Roles) */}
      {(() => {
        console.log('🚗 Trip Started notification check:', {
          userRole,
          hasTripStarted: !!tripStarted,
          tripStarted
        });
        return null;
      })()}
      {tripStarted && (
        <TripStartedNotification
          userRole={userRole}
          tripDetails={tripStarted.tripDetails}
          timestamp={tripStarted.timestamp}
          onViewTrip={handleDismissTripStarted}
        />
      )}



      {/* Driver Notifications */}
      {userRole === 'driver' && (
        <>
          {rideRequests.filter(request =>
            request.firstName && request.lastName &&
            request.firstName !== 'Unknown' && request.lastName !== 'User'
          ).map((request) => (
            <RideRequestNotification
              key={request.id}
              request={{
                id: request.id,
                passengerName: `${request.firstName}`,
                pickup: request.pickup,
                dropoff: request.dropoff,
                requestedAt: request.requestedAt,
                modeOfPayment: request.modeOfPayment,
                pricePerSeat: activeTrip?.pricePerSeat,
              }}
              tripId={activeTrip?.id || ''}
              tripData={{
                timestamp: activeTrip?.timestamp || '',
                pricePerSeat: activeTrip?.pricePerSeat || 0,
              }}
              fullTripData={activeTrip}
              onAccept={handleAcceptRequest}
              onDecline={handleDeclineRequest}
            />
          ))}
        </>
      )}

      {/* Pickup Confirmed Notifications (Passenger Only) */}
      {(() => {
        console.log('✅ Pickup Confirmed notification check:', {
          userRole,
          hasPickupConfirmed: !!pickupConfirmed,
          pickupConfirmed
        });
        return null;
      })()}
      {pickupConfirmed && !isDriver && (
        <PickupConfirmedNotification
          tripDetails={pickupConfirmed.tripDetails}
          passengerName={pickupConfirmed.passengerName}
          timestamp={pickupConfirmed.timestamp}
          onDismiss={handleDismissPickupConfirmed}
        />
      )}

      {/* Passenger Notifications */}
      {userRole === 'passenger' && (
        <>
          {console.log('🎭 Passenger notification check:', {
            userRole,
            hasBookingStatus: !!bookingStatus,
            bookingStatus,
            shouldRenderBookingStatus: userRole === 'passenger' && !!bookingStatus
          })}
          {bookingStatus && (
            <>
              {console.log('🎭 Rendering BookingStatusNotification with:', {
                status: bookingStatus.status,
                tripDetails: bookingStatus.tripDetails,
                timestamp: bookingStatus.timestamp
              })}
              <BookingStatusNotification
                status={bookingStatus.status}
                tripDetails={bookingStatus.tripDetails}
                fullTripData={bookingStatus.fullTripData}
                timestamp={bookingStatus.timestamp}
              />
            </>
          )}
        </>
      )}

      

      
    </View>
  );
});

// Set display name for debugging
RoleBasedNotifications.displayName = 'RoleBasedNotifications';

export default RoleBasedNotifications;
