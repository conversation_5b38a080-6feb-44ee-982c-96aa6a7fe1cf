import * as Notifications from 'expo-notifications';
import * as Device from 'expo-device';
import { Platform } from 'react-native';
import Constants from 'expo-constants';
import { router } from 'expo-router';

// Configure how notifications are handled when the app is in the foreground
Notifications.setNotificationHandler({
  handleNotification: async () => ({
    shouldShowAlert: true,
    shouldPlaySound: true,
    shouldSetBadge: true,
  }),
});

export interface PushNotificationData {
  type: 'trip_started' | 'pickup_confirmed' | 'trip_ended' | 'booking_accepted' | 'booking_declined' | 'payment_request' | 'trip_cancelled' | 'trip_reminder_30min' | 'trip_reminder_5min' | 'trip_start_now';
  tripId: string;
  title: string;
  body: string;
  data?: any;
  userRole?: 'driver' | 'passenger';
}

class PushNotificationService {
  private expoPushToken: string | null = null;
  private isInitialized: boolean = false;

  async initialize(): Promise<string | null> {
    try {
      // Try to register for push notifications, but fallback to local-only if it fails
      let token = null;
      try {
        token = await this.registerForPushNotificationsAsync();
        this.expoPushToken = token;
        console.log('✅ Push notifications with FCM token obtained:', token);
      } catch (tokenError) {
        console.warn('⚠️ FCM token failed, using local notifications only:', tokenError);
        // Still allow local notifications to work
        await this.setupLocalNotificationsOnly();
      }

      this.isInitialized = true;

      // Set up notification response listener
      this.setupNotificationResponseListener();

      console.log('✅ Push Notification Service initialized (local notifications ready)');
      return token;
    } catch (error) {
      console.error('❌ Failed to initialize push notification service:', error);
      return null;
    }
  }

  private async setupLocalNotificationsOnly(): Promise<void> {
    // Set up notification channels for Android (works without FCM)
    if (Platform.OS === 'android') {
      await Notifications.setNotificationChannelAsync('default', {
        name: 'default',
        importance: Notifications.AndroidImportance.MAX,
        vibrationPattern: [0, 250, 250, 250],
        lightColor: '#473BF0',
        sound: 'default',
      });

      await Notifications.setNotificationChannelAsync('trip-updates', {
        name: 'Trip Updates',
        importance: Notifications.AndroidImportance.HIGH,
        vibrationPattern: [0, 250, 250, 250],
        lightColor: '#473BF0',
        sound: 'default',
        description: 'Notifications for trip status updates',
      });

      await Notifications.setNotificationChannelAsync('trip-reminders', {
        name: 'Trip Reminders',
        importance: Notifications.AndroidImportance.MAX,
        vibrationPattern: [0, 500, 250, 500],
        lightColor: '#FF9500',
        sound: 'default',
        description: 'Notifications for upcoming trip reminders',
      });
    }

    // Request basic notification permissions
    const { status: existingStatus } = await Notifications.getPermissionsAsync();
    if (existingStatus !== 'granted') {
      await Notifications.requestPermissionsAsync();
    }
  }

  private setupNotificationResponseListener() {
    // Handle notification taps when app is in foreground or background
    Notifications.addNotificationResponseReceivedListener(response => {
      const data = response.notification.request.content.data;
      console.log('📱 Notification tapped:', data);

      // Navigate to home page for all notification types
      router.replace('/(tabs)/Home');
    });
  }

  private async registerForPushNotificationsAsync(): Promise<string | null> {
    let token: string | null = null;

    // Set up notification channels first (works without FCM)
    if (Platform.OS === 'android') {
      await Notifications.setNotificationChannelAsync('default', {
        name: 'default',
        importance: Notifications.AndroidImportance.MAX,
        vibrationPattern: [0, 250, 250, 250],
        lightColor: '#473BF0',
        sound: 'default',
      });

      await Notifications.setNotificationChannelAsync('trip-updates', {
        name: 'Trip Updates',
        importance: Notifications.AndroidImportance.HIGH,
        vibrationPattern: [0, 250, 250, 250],
        lightColor: '#473BF0',
        sound: 'default',
        description: 'Notifications for trip status updates',
      });

      await Notifications.setNotificationChannelAsync('booking-updates', {
        name: 'Booking Updates',
        importance: Notifications.AndroidImportance.HIGH,
        vibrationPattern: [0, 250, 250, 250],
        lightColor: '#34A853',
        sound: 'default',
        description: 'Notifications for booking confirmations and updates',
      });

      await Notifications.setNotificationChannelAsync('trip-reminders', {
        name: 'Trip Reminders',
        importance: Notifications.AndroidImportance.MAX,
        vibrationPattern: [0, 500, 250, 500],
        lightColor: '#FF9500',
        sound: 'default',
        description: 'Notifications for upcoming trip reminders',
      });
    }

    if (!Device.isDevice) {
      console.warn('❌ Must use physical device for push notifications');
      throw new Error('Physical device required for push notifications');
    }

    // Request permissions
    const { status: existingStatus } = await Notifications.getPermissionsAsync();
    let finalStatus = existingStatus;

    if (existingStatus !== 'granted') {
      const { status } = await Notifications.requestPermissionsAsync();
      finalStatus = status;
    }

    if (finalStatus !== 'granted') {
      throw new Error('Push notification permission not granted');
    }

    // Try to get Expo push token (requires FCM on Android)
    const projectId = Constants?.expoConfig?.extra?.eas?.projectId;
    if (!projectId) {
      throw new Error('Project ID not found - required for Expo push tokens');
    }

    // This is where the Firebase error occurs
    token = (await Notifications.getExpoPushTokenAsync({ projectId })).data;
    console.log('✅ Expo push token obtained:', token);

    return token;
  }

  async sendLocalNotification(notificationData: PushNotificationData): Promise<void> {
    if (!this.isInitialized) {
      console.warn('⚠️ Push notification service not initialized');
      return;
    }

    try {
      const channelId = this.getChannelIdForType(notificationData.type);

      await Notifications.scheduleNotificationAsync({
        content: {
          title: notificationData.title,
          body: notificationData.body,
          data: {
            ...notificationData.data,
            type: notificationData.type,
            tripId: notificationData.tripId,
            userRole: notificationData.userRole,
          },
          sound: 'default',
          priority: Notifications.AndroidNotificationPriority.HIGH,
          ...(Platform.OS === 'android' && { channelId }),
        },
        trigger: null, // Show immediately
        identifier: `${notificationData.type}_${notificationData.tripId}_${Date.now()}`,
      });

      console.log('✅ Local notification sent:', notificationData.title);
    } catch (error) {
      console.error('❌ Failed to send local notification:', error);
    }
  }

  // Schedule a notification for a specific time
  async scheduleNotificationAt(notificationData: PushNotificationData, triggerDate: Date): Promise<string> {
    if (!this.isInitialized) {
      console.warn('⚠️ Push notification service not initialized');
      return '';
    }

    try {
      const channelId = this.getChannelIdForType(notificationData.type);
      const identifier = `${notificationData.type}_${notificationData.tripId}_${triggerDate.getTime()}`;

      await Notifications.scheduleNotificationAsync({
        content: {
          title: notificationData.title,
          body: notificationData.body,
          data: {
            ...notificationData.data,
            type: notificationData.type,
            tripId: notificationData.tripId,
            userRole: notificationData.userRole,
          },
          sound: 'default',
          priority: Notifications.AndroidNotificationPriority.HIGH,
          ...(Platform.OS === 'android' && { channelId }),
        },
        trigger: triggerDate,
        identifier,
      });

      console.log('✅ Scheduled notification for:', triggerDate.toLocaleString(), notificationData.title);
      return identifier;
    } catch (error) {
      console.error('❌ Failed to schedule notification:', error);
      return '';
    }
  }

  // Cancel a scheduled notification
  async cancelScheduledNotification(identifier: string): Promise<void> {
    try {
      await Notifications.cancelScheduledNotificationAsync(identifier);
      console.log('✅ Cancelled scheduled notification:', identifier);
    } catch (error) {
      console.error('❌ Failed to cancel scheduled notification:', error);
    }
  }

  // Cancel all notifications for a specific trip
  async cancelTripNotifications(tripId: string): Promise<void> {
    try {
      const scheduledNotifications = await Notifications.getAllScheduledNotificationsAsync();
      const tripNotifications = scheduledNotifications.filter(
        notification => notification.content.data?.tripId === tripId
      );

      for (const notification of tripNotifications) {
        await Notifications.cancelScheduledNotificationAsync(notification.identifier);
      }

      console.log(`✅ Cancelled ${tripNotifications.length} notifications for trip:`, tripId);
    } catch (error) {
      console.error('❌ Failed to cancel trip notifications:', error);
    }
  }

  private getChannelIdForType(type: string): string {
    switch (type) {
      case 'trip_started':
      case 'trip_ended':
      case 'pickup_confirmed':
        return 'trip-updates';
      case 'booking_accepted':
      case 'booking_declined':
        return 'booking-updates';
      case 'trip_reminder_30min':
      case 'trip_reminder_5min':
      case 'trip_start_now':
        return 'trip-reminders';
      default:
        return 'default';
    }
  }

  getExpoPushToken(): string | null {
    return this.expoPushToken;
  }

  isServiceInitialized(): boolean {
    return this.isInitialized;
  }

  // Helper methods for common notification types
  async notifyTripStarted(tripId: string, driverName: string): Promise<void> {
    await this.sendLocalNotification({
      type: 'trip_started',
      tripId,
      title: '🚗 Trip Started',
      body: `${driverName} has started your ride. Track your progress in real-time.`,
      data: { driverName },
      userRole: 'passenger'
    });
  }

  // Role-based trip started notifications
  async notifyTripStartedForDriver(tripId: string, passengerCount: number): Promise<void> {
    await this.sendLocalNotification({
      type: 'trip_started',
      tripId,
      title: '🚗 Trip Started Successfully',
      body: `Your trip has started with ${passengerCount} passenger${passengerCount > 1 ? 's' : ''}. Drive safely!`,
      data: { passengerCount },
      userRole: 'driver'
    });
  }

  async notifyTripStartedForPassenger(tripId: string, driverName: string): Promise<void> {
    await this.sendLocalNotification({
      type: 'trip_started',
      tripId,
      title: '🚗 Your Ride Has Started',
      body: `${driverName} has started your trip. You can track your progress in real-time.`,
      data: { driverName },
      userRole: 'passenger'
    });
  }

  async notifyPickupConfirmed(tripId: string, passengerName: string): Promise<void> {
    await this.sendLocalNotification({
      type: 'pickup_confirmed',
      tripId,
      title: '✅ Pickup Confirmed',
      body: `${passengerName}'s pickup has been confirmed.`,
      data: { passengerName }
    });
  }

  async notifyTripEnded(tripId: string): Promise<void> {
    await this.sendLocalNotification({
      type: 'trip_ended',
      tripId,
      title: '🎉 Trip Completed',
      body: 'Your ride has been completed successfully.',
      data: {}
    });
  }

  // Role-based trip ended notifications
  async notifyTripEndedForDriver(tripId: string, earnings?: string): Promise<void> {
    await this.sendLocalNotification({
      type: 'trip_ended',
      tripId,
      title: '🎉 Trip Completed Successfully',
      body: earnings ? `Trip completed! You earned ${earnings}.` : 'Trip completed successfully. Great job!',
      data: { earnings },
      userRole: 'driver'
    });
  }

  async notifyTripEndedForPassenger(tripId: string, driverName: string): Promise<void> {
    await this.sendLocalNotification({
      type: 'trip_ended',
      tripId,
      title: '🎉 Trip Completed',
      body: `Your ride with ${driverName} has been completed. We hope you enjoyed your trip!`,
      data: { driverName },
      userRole: 'passenger'
    });
  }

  async notifyTripCancelled(tripId: string, cancelledBy: string, reason?: string): Promise<void> {
    await this.sendLocalNotification({
      type: 'trip_cancelled',
      tripId,
      title: '❌ Trip Cancelled',
      body: `Your trip has been cancelled by ${cancelledBy}.${reason ? ` Reason: ${reason}` : ''}`,
      data: { cancelledBy, reason }
      // userRole omitted for general cancellation - will be handled by role-specific methods
    });
  }

  // Role-based trip cancellation notifications
  async notifyTripCancelledForDriver(tripId: string, reason?: string): Promise<void> {
    await this.sendLocalNotification({
      type: 'trip_cancelled',
      tripId,
      title: '❌ Trip Cancelled',
      body: reason ? `Trip cancelled. Reason: ${reason}` : 'Trip has been cancelled successfully.',
      data: { reason },
      userRole: 'driver'
    });
  }

  async notifyTripCancelledForPassenger(tripId: string, driverName: string, reason?: string): Promise<void> {
    await this.sendLocalNotification({
      type: 'trip_cancelled',
      tripId,
      title: '😔 Trip Cancelled',
      body: `${driverName} has cancelled your trip.${reason ? ` Reason: ${reason}` : ''} We apologize for the inconvenience.`,
      data: { driverName, reason },
      userRole: 'passenger'
    });
  }

  // Time-based trip reminder notifications
  async notifyTripReminder30Min(tripId: string, userRole: 'driver' | 'passenger', tripDetails: { origin: string, destination: string, time: string }): Promise<void> {
    const isDriver = userRole === 'driver';
    await this.sendLocalNotification({
      type: 'trip_reminder_30min',
      tripId,
      title: isDriver ? '🚗 Trip Starting Soon' : '🚗 Your Ride is Coming',
      body: isDriver
        ? `Your trip from ${tripDetails.origin} to ${tripDetails.destination} starts in 30 minutes at ${tripDetails.time}. Get ready!`
        : `Your ride from ${tripDetails.origin} to ${tripDetails.destination} starts in 30 minutes at ${tripDetails.time}. Be ready!`,
      data: { ...tripDetails, reminderType: '30min' },
      userRole
    });
  }

  async notifyTripReminder5Min(tripId: string, userRole: 'driver' | 'passenger', tripDetails: { origin: string, destination: string, time: string }): Promise<void> {
    const isDriver = userRole === 'driver';
    await this.sendLocalNotification({
      type: 'trip_reminder_5min',
      tripId,
      title: isDriver ? '🚗 Trip Starting Very Soon!' : '🚗 Your Ride is Almost Here!',
      body: isDriver
        ? `Your trip starts in 5 minutes! Time to head to ${tripDetails.origin} and pick up your passengers.`
        : `Your ride starts in 5 minutes! Be ready at ${tripDetails.origin} for pickup.`,
      data: { ...tripDetails, reminderType: '5min' },
      userRole
    });
  }

  async notifyTripStartNow(tripId: string, userRole: 'driver' | 'passenger', tripDetails: { origin: string, destination: string }): Promise<void> {
    const isDriver = userRole === 'driver';
    await this.sendLocalNotification({
      type: 'trip_start_now',
      tripId,
      title: isDriver ? '🚗 Time to Start Your Trip!' : '🚗 Your Ride Should Start Now!',
      body: isDriver
        ? `It's time to start your trip! Head to ${tripDetails.origin} to pick up your passengers.`
        : `Your ride should be starting now. Your driver should be arriving at ${tripDetails.origin}.`,
      data: { ...tripDetails, reminderType: 'now' },
      userRole
    });
  }



  async notifyBookingAccepted(tripId: string, driverName: string): Promise<void> {
    await this.sendLocalNotification({
      type: 'booking_accepted',
      tripId,
      title: '✅ Booking Accepted',
      body: `Great news! ${driverName} has accepted your ride request.`,
      data: { driverName }
    });
  }

  async notifyBookingDeclined(tripId: string, driverName: string): Promise<void> {
    await this.sendLocalNotification({
      type: 'booking_declined',
      tripId,
      title: '❌ Booking Declined',
      body: `${driverName} couldn't accept your ride request. Don't worry, you can find another trip.`,
      data: { driverName },
      userRole: 'passenger'
    });
  }


}

// Export singleton instance
export const pushNotificationService = new PushNotificationService();

// Helper functions for easy access
export const pushNotificationHelpers = {
  initialize: () => pushNotificationService.initialize(),

  // Legacy methods (backward compatibility)
  notifyTripStarted: (tripId: string, driverName: string) =>
    pushNotificationService.notifyTripStarted(tripId, driverName),
  notifyPickupConfirmed: (tripId: string, passengerName: string) =>
    pushNotificationService.notifyPickupConfirmed(tripId, passengerName),
  notifyTripEnded: (tripId: string) =>
    pushNotificationService.notifyTripEnded(tripId),
  notifyBookingAccepted: (tripId: string, driverName: string) =>
    pushNotificationService.notifyBookingAccepted(tripId, driverName),
  notifyBookingDeclined: (tripId: string, driverName: string) =>
    pushNotificationService.notifyBookingDeclined(tripId, driverName),

  // Role-based notifications
  notifyTripStartedForDriver: (tripId: string, passengerCount: number) =>
    pushNotificationService.notifyTripStartedForDriver(tripId, passengerCount),
  notifyTripStartedForPassenger: (tripId: string, driverName: string) =>
    pushNotificationService.notifyTripStartedForPassenger(tripId, driverName),
  notifyTripEndedForDriver: (tripId: string, earnings?: string) =>
    pushNotificationService.notifyTripEndedForDriver(tripId, earnings),
  notifyTripEndedForPassenger: (tripId: string, driverName: string) =>
    pushNotificationService.notifyTripEndedForPassenger(tripId, driverName),

  // Time-based trip reminders
  notifyTripReminder30Min: (tripId: string, userRole: 'driver' | 'passenger', tripDetails: any) =>
    pushNotificationService.notifyTripReminder30Min(tripId, userRole, tripDetails),
  notifyTripReminder5Min: (tripId: string, userRole: 'driver' | 'passenger', tripDetails: any) =>
    pushNotificationService.notifyTripReminder5Min(tripId, userRole, tripDetails),
  notifyTripStartNow: (tripId: string, userRole: 'driver' | 'passenger', tripDetails: any) =>
    pushNotificationService.notifyTripStartNow(tripId, userRole, tripDetails),

  // Trip cancellation notifications
  notifyTripCancelled: (tripId: string, cancelledBy: string, reason?: string) =>
    pushNotificationService.notifyTripCancelled(tripId, cancelledBy, reason),
  notifyTripCancelledForDriver: (tripId: string, reason?: string) =>
    pushNotificationService.notifyTripCancelledForDriver(tripId, reason),
  notifyTripCancelledForPassenger: (tripId: string, driverName: string, reason?: string) =>
    pushNotificationService.notifyTripCancelledForPassenger(tripId, driverName, reason),

  // Utility methods
  getToken: () => pushNotificationService.getExpoPushToken(),
  isInitialized: () => pushNotificationService.isServiceInitialized(),
};
