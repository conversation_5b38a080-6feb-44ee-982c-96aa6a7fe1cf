import { Realtime } from 'ably';
import { TripEndedEventData } from './passengerTripEndedListener';

interface Trip {
  id: string;
  origin: { name: string };
  destination: { name: string };
  passengers: Array<{ id: string; firstName?: string; lastName?: string }>;
  price?: string;
  timestamp?: string;
}

interface Driver {
  id: string;
  firstName: string;
  lastName: string;
  profilePicture?: string;
}

class DriverTripEndedPublisher {
  private ably: Realtime | null = null;
  private isInitialized: boolean = false;

  /**
   * Initialize the publisher with Ably instance
   */
  initialize(ably: Realtime): void {
    this.ably = ably;
    this.isInitialized = true;
    console.log('📡 DriverTripEndedPublisher initialized');
  }

  /**
   * Publish trip-ended event to all passengers
   */
  async publishTripEnded(
    trip: Trip, 
    driver: Driver, 
    endReason: 'completed' | 'early' | 'cancelled' | 'issue' = 'completed'
  ): Promise<void> {
    if (!this.isInitialized || !this.ably) {
      console.warn('⚠️ DriverTripEndedPublisher not initialized');
      return;
    }

    try {
      // Check Ably connection state before publishing
      console.log('🔍 DEBUG: Ably connection state:', this.ably.connection.state);

      if (this.ably.connection.state !== 'connected') {
        console.warn('⚠️ Ably not connected, current state:', this.ably.connection.state);

        // If connecting, wait for it to complete
        if (this.ably.connection.state === 'connecting') {
          console.log('🔄 Ably is connecting, waiting...');
          await new Promise((resolve, reject) => {
            const timeout = setTimeout(() => {
              reject(new Error('Connection timeout - took longer than 10 seconds'));
            }, 10000); // Increased to 10 seconds

            this.ably.connection.once('connected', () => {
              clearTimeout(timeout);
              console.log('✅ Ably connected successfully');
              resolve(void 0);
            });
            this.ably.connection.once('failed', () => {
              clearTimeout(timeout);
              reject(new Error('Ably connection failed'));
            });
            this.ably.connection.once('disconnected', () => {
              clearTimeout(timeout);
              reject(new Error('Ably connection disconnected'));
            });
          });
        } else {
          // Try to connect if not connecting
          console.log('🔄 Attempting to connect to Ably...');
          this.ably.connection.connect();
          await new Promise((resolve, reject) => {
            const timeout = setTimeout(() => {
              reject(new Error('Connection timeout - could not connect within 10 seconds'));
            }, 10000);

            this.ably.connection.once('connected', () => {
              clearTimeout(timeout);
              console.log('✅ Ably connected successfully');
              resolve(void 0);
            });
            this.ably.connection.once('failed', () => {
              clearTimeout(timeout);
              reject(new Error('Ably connection failed'));
            });
          });
        }
      }

      const channelName = `trip-${trip.id}`;
      const channel = this.ably.channels.get(channelName);

      // Calculate trip duration if possible
      const duration = this.calculateTripDuration(trip.timestamp);

      // Prepare event data
      const eventData: TripEndedEventData = {
        tripId: trip.id,
        driverName: `${driver.firstName} ${driver.lastName}`,
        driverPhoto: driver.profilePicture,
        origin: this.formatLocation(trip.origin.name),
        destination: this.formatLocation(trip.destination.name),
        duration,
        cost: trip.price,
        endReason,
        endTime: new Date().toISOString(),
        fullTripData: trip, // Include full trip data for car details
      };

      console.log('🔍 DEBUG: About to publish trip-ended events', {
        channelName,
        eventData,
        passengerCount: trip.passengers.length,
        timestamp: new Date().toISOString()
      });

      // Publish to all passengers with retry logic
      for (const passenger of trip.passengers) {
        const passengerEventData = {
          ...eventData,
          passengerId: passenger.id, // Include passenger ID for filtering
        };

        console.log('🔍 DEBUG: Publishing to specific passenger', {
          passengerId: passenger.id,
          passengerEventData
        });

        // Retry logic for passenger events
        let retries = 3;
        while (retries > 0) {
          try {
            await channel.publish('trip-ended', passengerEventData);
            console.log(`📡 Published trip-ended event to passenger ${passenger.id} on channel:`, channelName);
            break; // Success, exit retry loop
          } catch (error) {
            retries--;
            console.warn(`⚠️ Failed to publish to passenger ${passenger.id}, retries left: ${retries}`, error);
            if (retries === 0) throw error; // Re-throw if no retries left
            await new Promise(resolve => setTimeout(resolve, 1000)); // Wait 1 second before retry
          }
        }
      }

      // Also publish a general event without passenger ID (for backward compatibility)
      console.log('🔍 DEBUG: Publishing general event', { eventData });

      // Retry logic for general event
      let retries = 3;
      while (retries > 0) {
        try {
          await channel.publish('trip-ended', eventData);
          console.log('📡 Published general trip-ended event on channel:', channelName);
          break; // Success, exit retry loop
        } catch (error) {
          retries--;
          console.warn(`⚠️ Failed to publish general event, retries left: ${retries}`, error);
          if (retries === 0) throw error; // Re-throw if no retries left
          await new Promise(resolve => setTimeout(resolve, 1000)); // Wait 1 second before retry
        }
      }

      console.log('✅ Successfully published trip-ended events for trip:', trip.id);
    } catch (error) {
      console.error('❌ Failed to publish trip-ended event:', error);

      // If it's a connection timeout, provide more context
      if (error instanceof Error && error.message.includes('timeout')) {
        console.error('💡 Suggestion: Check internet connection and Ably service status');
        throw new Error(`Ably connection timeout: ${error.message}`);
      }

      throw error;
    }
  }

  /**
   * Publish trip cancelled event (specific type of trip ended)
   */
  async publishTripCancelled(trip: Trip, driver: Driver, reason?: string): Promise<void> {
    console.log('🚫 Publishing trip cancelled event:', { tripId: trip.id, reason });
    await this.publishTripEnded(trip, driver, 'cancelled');
  }

  /**
   * Publish trip ended early event
   */
  async publishTripEndedEarly(trip: Trip, driver: Driver, reason?: string): Promise<void> {
    console.log('⏰ Publishing trip ended early event:', { tripId: trip.id, reason });
    await this.publishTripEnded(trip, driver, 'early');
  }

  /**
   * Publish trip ended due to issue
   */
  async publishTripEndedWithIssue(trip: Trip, driver: Driver, issue?: string): Promise<void> {
    console.log('⚠️ Publishing trip ended with issue event:', { tripId: trip.id, issue });
    await this.publishTripEnded(trip, driver, 'issue');
  }

  /**
   * Format location name for display
   */
  private formatLocation(locationName: string): string {
    // Take only the first part before comma for cleaner display
    return locationName.split(',')[0].trim();
  }

  /**
   * Calculate trip duration if start time is available
   */
  private calculateTripDuration(startTime?: string): string | undefined {
    if (!startTime) return undefined;

    try {
      const start = new Date(startTime);
      const end = new Date();
      const durationMs = end.getTime() - start.getTime();
      
      const minutes = Math.floor(durationMs / (1000 * 60));
      const hours = Math.floor(minutes / 60);
      const remainingMinutes = minutes % 60;

      if (hours > 0) {
        return `${hours}h ${remainingMinutes}m`;
      } else {
        return `${minutes}m`;
      }
    } catch (error) {
      console.warn('⚠️ Failed to calculate trip duration:', error);
      return undefined;
    }
  }

  /**
   * Check if publisher is ready
   */
  isReady(): boolean {
    return this.isInitialized && this.ably !== null;
  }

  /**
   * Cleanup resources
   */
  cleanup(): void {
    this.ably = null;
    this.isInitialized = false;
    console.log('🧹 DriverTripEndedPublisher cleaned up');
  }
}

// Export singleton instance
export const driverTripEndedPublisher = new DriverTripEndedPublisher();

// Helper functions for easy access
export const driverTripEndedPublisherHelpers = {
  initialize: (ably: Realtime) => 
    driverTripEndedPublisher.initialize(ably),
  publishTripEnded: (trip: Trip, driver: Driver, endReason?: 'completed' | 'early' | 'cancelled' | 'issue') => 
    driverTripEndedPublisher.publishTripEnded(trip, driver, endReason),
  publishTripCancelled: (trip: Trip, driver: Driver, reason?: string) => 
    driverTripEndedPublisher.publishTripCancelled(trip, driver, reason),
  publishTripEndedEarly: (trip: Trip, driver: Driver, reason?: string) => 
    driverTripEndedPublisher.publishTripEndedEarly(trip, driver, reason),
  publishTripEndedWithIssue: (trip: Trip, driver: Driver, issue?: string) => 
    driverTripEndedPublisher.publishTripEndedWithIssue(trip, driver, issue),
  isReady: () => 
    driverTripEndedPublisher.isReady(),
  cleanup: () => 
    driverTripEndedPublisher.cleanup(),
};

export type { Trip, Driver };
