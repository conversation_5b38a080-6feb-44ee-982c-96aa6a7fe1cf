import { View, Text, TouchableOpacity, Image, Alert } from "react-native";
import React, { useEffect, useState } from "react";
import * as WebBrowser from "expo-web-browser";
import * as Google from "expo-auth-session/providers/google";
import * as Facebook from "expo-auth-session/providers/facebook";
import * as SecureStore from "expo-secure-store";
import { router } from "expo-router";
import { services } from "@/services";
import {
  IOS_CLIENT_ID,
  WEB_CLIENT_ID,
  FACEBOOK_APP_ID,
  ANDROID_CLIENT_ID,
} from "@/keys";
// devemmah.
const SocialSignIn = () => {
  const [request, response, promptAsync] = Google.useAuthRequest({
    clientId: WEB_CLIENT_ID,
    androidClientId: ANDROID_CLIENT_ID,
    iosClientId: IOS_CLIENT_ID,
    webClientId: WEB_CLIENT_ID,
    scopes: ["profile", "email"],
  });

  const [facebookRequest, facebookResponse, promptFacebookAsync] =
    Facebook.useAuthRequest({
      clientId: FACEBOOK_APP_ID,
      scopes: ["public_profile", "email"],
    });

  useEffect(() => {
    handleSignInResponse();
  }, [response, facebookResponse]);

  const handleSignInResponse = async () => {
    if (response?.type === "success") {
      const { authentication } = response;
      await handleGoogleSignIn(authentication?.accessToken);
    } else if (facebookResponse?.type === "success") {
      const { authentication } = facebookResponse;
      await handleFacebookSignIn(authentication?.accessToken);
    }
  };

  const handleGoogleSignIn = async (accessToken: string | undefined) => {
    if (!accessToken) {
      Alert.alert("Error", "No access token received from Google");
      return;
    }

    try {
      const response = await services.loginWithGoogle({
        dataBody: { accessToken }
      });

      if (response.status && response.data) {
        // Store the token securely
        const tokenData = {
          token: response.data.token,
          expiration: Date.now() + (7 * 24 * 60 * 60 * 1000) // 7 days from now
        };
        await SecureStore.setItemAsync("authToken", JSON.stringify(tokenData));

        console.log("Google login successful", response.data);
        Alert.alert(
          "Success", 
          `Welcome back ${response.data.firstName}!`,
          [{
            text: "OK",
            onPress: () => router.replace("/Home")
          }]
        );
      } else {
        Alert.alert("Error", response.message || "Google login failed");
      }
    } catch (error: any) {
      console.error("Error during Google login:", error);
      const errorMessage = error?.response?.data?.message || "An error occurred during Google sign-in";
      Alert.alert("Error", errorMessage);
    }
  };

  const handleFacebookSignIn = async (accessToken: string | undefined) => {
    // TODO: Implement Facebook signup endpoint in services
    Alert.alert(
      "Coming Soon", 
      "Facebook sign-in is not yet implemented. Please use Google sign-in or create an account manually."
    );
    console.log("Facebook sign-in attempted but not implemented yet", { accessToken });
  };

  return (
    <View className="gap-y-5">
      <TouchableOpacity
        onPress={() => {
          console.log("Attempting to sign in with Google...");
          promptAsync();
        }}
        className="relative border border-[#151B2D33] flex flex-row justify-center items-center py-[15px] rounded-[100px]"
      >
        <Image
          source={require("../assets/images/Google.png")}
          className="w-[16px] h-[16px] absolute left-3"
          resizeMode="contain"
        />
        <Text className="text-center text-[#151B2D] font-medium text-[15px]">
          Continue with Google
        </Text>
      </TouchableOpacity>

      <TouchableOpacity
        onPress={() => {
          console.log("Attempting to sign in with Facebook...");
          promptFacebookAsync();
        }}
        className="relative border border-[#151B2D33] flex flex-row justify-center items-center py-[15px] rounded-[100px]"
      >
        <Image
          source={require("../assets/images/Facebook.png")}
          className="w-[16px] h-[16px] absolute left-3"
          resizeMode="contain"
        />
        <Text className="text-center text-[#151B2D] font-medium text-[15px]">
          Continue with Facebook
        </Text>
      </TouchableOpacity>
    </View>
  );
};

export default SocialSignIn;
